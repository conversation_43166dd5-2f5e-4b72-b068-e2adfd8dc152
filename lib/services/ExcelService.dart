import 'dart:convert';
import 'dart:io';
import 'package:excel/excel.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../helper/model.dart';
import '../repositories/CalibrationRepository.dart';
import 'PdfService.dart';

class ExcelService {
  final CalibrationRepository repository;

  ExcelService({required this.repository});

  Future<String> generateCalibrationExcel(String requestId) async {
    try {
      // Get the calibration request and related data
      final request = await repository.getCalibrationRequestByUuid(requestId);
      if (request == null) {
        throw Exception('Calibration request not found');
      }

      // Get timbangan data if available
      Map<String, dynamic>? timbangan;
      if (request.timbanganUuid != null) {
        timbangan = await repository.getMasterTimbanganByUuid(
          request.timbanganUuid!,
        );
      }

      // Get technician data if available
      Map<String, dynamic>? technician;
      if (request.technicianUuid != null) {
        technician = await repository.getMasterTechnicianByUuid(
          request.technicianUuid!,
        );
      }

      // Parse allMetadata to get complete form data
      Map<String, dynamic> allMetadata = {};
      if (request.allMetadata != null && request.allMetadata!.isNotEmpty) {
        try {
          allMetadata = jsonDecode(request.allMetadata!);
        } catch (e) {
          print('Error parsing allMetadata: $e');
        }
      }

      // Get related data (still needed for compatibility)
      final repeatCapacities = await repository.getRepeatCapacitiesByRequestId(
        requestId,
      );
      final linearities = await repository.getLinearitiesByRequestId(requestId);
      final eccentricities = await repository.getEccentricitiesByRequestId(
        requestId,
      );
      final histerisys = await repository.getHisterisysByRequestId(requestId);

      // Create Excel workbook
      final excel = Excel.createExcel();

      // Remove default sheet
      excel.delete('Sheet1');

      // Create main sheet
      final mainSheet = excel['Calibration_Report'];

      // Add ALMEGA logo
      await _addAlmegaLogo(excel, mainSheet);

      // Add header information
      _addHeaderInfo(mainSheet, request, timbangan, technician);

      // Create technical report sheet
      final techSheet = excel['Technical_Report'];
      await _addTechnicalReportSheet(
        techSheet,
        request,
        timbangan,
        technician,
        allMetadata,
        repeatCapacities,
        linearities,
        eccentricities,
        histerisys,
      );

      // Add calibration data sections in ALMEGA format
      int currentRow = 20; // Start after header section and logo

      // Add Pre-adjustment section
      currentRow = _addPreAdjustmentSection(
        mainSheet,
        request,
        allMetadata,
        currentRow,
      );

      // Add Linearity section (Ketelitian Baca Berulang)
      currentRow = _addLinearitySection(
        mainSheet,
        linearities,
        allMetadata,
        currentRow + 2,
      );

      // Add Eccentricity section
      currentRow = _addEccentricitySection(
        mainSheet,
        eccentricities,
        allMetadata,
        currentRow + 2,
      );

      // Add Repeat Capacity section
      currentRow = _addRepeatCapacitySection(
        mainSheet,
        repeatCapacities,
        allMetadata,
        currentRow + 2,
      );

      // Add Hysteresis section
      currentRow = _addHysteresisSection(
        mainSheet,
        histerisys,
        allMetadata,
        currentRow + 2,
      );

      // Save the file
      final directory = await getApplicationDocumentsDirectory();

      // Ensure the directory exists
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Create a subdirectory for Excel reports
      final reportsDir = Directory('${directory.path}/excel_reports');
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      // Sanitize the order number for file name (replace invalid characters)
      final sanitizedOrderNumber = request.orderNumber
          .replaceAll(RegExp(r'[<>:"/\\|?*\s]'), '_')
          .replaceAll(
            RegExp(r'_+'),
            '_',
          ); // Replace multiple underscores with single

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName =
          'Calibration_Report_${sanitizedOrderNumber}_$timestamp.xlsx';
      final filePath = '${reportsDir.path}/$fileName';

      print('Attempting to save Excel file to: $filePath'); // Debug log
      print('Sanitized order number: $sanitizedOrderNumber'); // Debug log

      final file = File(filePath);
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to encode Excel data');
      }

      await file.writeAsBytes(excelBytes);

      print('Excel file saved successfully to: $filePath'); // Debug log

      // Automatically generate PDF after Excel creation
      try {
        final pdfService = PdfService(repository: repository);
        final pdfPath = await pdfService.generateCalibrationPdf(requestId);
        print('PDF file also generated successfully to: $pdfPath'); // Debug log
      } catch (pdfError) {
        print(
          'PDF generation failed, but Excel was successful: $pdfError',
        ); // Debug log
        // Don't throw error for PDF failure, Excel generation was successful
      }

      return filePath;
    } catch (e) {
      throw Exception('Failed to generate Excel file: $e');
    }
  }

  // Add comprehensive technical report sheet
  Future<void> _addTechnicalReportSheet(
    Sheet sheet,
    CalibrationRequest request,
    Map<String, dynamic>? timbangan,
    Map<String, dynamic>? technician,
    Map<String, dynamic> allMetadata,
    List<CalibrationRepeatCapacity> repeatCapacities,
    List<CalibrationLinearity> linearities,
    List<CalibrationEccentricity> eccentricities,
    List<CalibrationHisterisys> histerisys,
  ) async {
    int row = 0;

    // Title
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'TECHNICAL CALIBRATION REPORT',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      backgroundColorHex: ExcelColor.blue200,
    );
    sheet.merge(
      CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row),
      CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row),
    );
    row += 2;

    // Equipment Summary
    row = _addTechEquipmentSummary(sheet, request, timbangan, technician, row);
    row += 2;

    // Test Summary
    row = _addTechTestSummary(sheet, allMetadata, row);
    row += 2;

    // Detailed Results
    row = _addTechDetailedResults(
      sheet,
      allMetadata,
      repeatCapacities,
      linearities,
      eccentricities,
      histerisys,
      row,
    );
    row += 2;

    // Calculations and Formulas
    row = _addTechCalculations(sheet, allMetadata, row);
    row += 2;

    // Uncertainty Analysis
    row = _addTechUncertaintyAnalysis(sheet, allMetadata, row);
    row += 2;

    // Compliance Assessment
    row = _addTechComplianceAssessment(sheet, allMetadata, row);
  }

  // Add ALMEGA logo to Excel sheet
  Future<void> _addAlmegaLogo(Excel excel, Sheet sheet) async {
    try {
      // Load the logo image from assets
      final ByteData logoData = await rootBundle.load(
        'assets/images/logo-almega.png',
      );
      final Uint8List logoBytes = logoData.buffer.asUint8List();

      // Add the image to Excel (Note: Excel package has limited image support)
      // For now, we'll add a placeholder text where the logo should be
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(
        'ALMEGA LOGO',
      );
      sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
        bold: true,
        fontSize: 14,
        backgroundColorHex: ExcelColor.blue100,
      );

      // Merge cells for logo area
      sheet.merge(CellIndex.indexByString('A1'), CellIndex.indexByString('B2'));
    } catch (e) {
      print('Error loading ALMEGA logo: $e');
      // Fallback to text-based logo
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('ALMEGA');
      sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
        bold: true,
        fontSize: 16,
        backgroundColorHex: ExcelColor.blue100,
      );
    }
  }

  void _addHeaderInfo(
    Sheet sheet,
    CalibrationRequest request,
    Map<String, dynamic>? timbangan,
    Map<String, dynamic>? technician,
  ) {
    // ALMEGA Header - Left side
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('ALMEGA');
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
    );

    // Certificate title - Left side
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(
      'LEMBAR KERJA KALIBRASI TIMBANGAN ELEKTRONIK',
    );
    sheet.cell(CellIndex.indexByString('A2')).cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );

    // ALMEGA Header - Right side
    sheet.cell(CellIndex.indexByString('H1')).value = TextCellValue('ALMEGA');
    sheet.cell(CellIndex.indexByString('H1')).cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
    );

    // Order number - Right side
    sheet.cell(CellIndex.indexByString('H2')).value = TextCellValue(
      'Order No.',
    );
    sheet.cell(CellIndex.indexByString('I2')).value = TextCellValue(
      request.orderNumber,
    );

    // Equipment Information Section - Left side
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue(
      'Nama Alat',
    );
    sheet.cell(CellIndex.indexByString('B4')).value = TextCellValue(
      'Timbangan Elektronik',
    );

    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue(
      'Kapasitas',
    );
    sheet.cell(CellIndex.indexByString('B5')).value = TextCellValue(
      timbangan?['capacity'] ?? 'N/A',
    );

    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Merk');
    sheet.cell(CellIndex.indexByString('B6')).value = TextCellValue(
      timbangan?['brand'] ?? '',
    );

    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue(
      'Type / Model',
    );
    sheet.cell(CellIndex.indexByString('B7')).value = TextCellValue(
      timbangan?['model'] ?? '',
    );

    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue(
      'Serial Number',
    );
    sheet.cell(CellIndex.indexByString('B8')).value = TextCellValue(
      timbangan?['snr'] ?? '',
    );

    sheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('Resolusi');
    sheet.cell(CellIndex.indexByString('B9')).value = TextCellValue(
      timbangan?['resolution'] ?? '',
    );

    // Sender and Location info - Right side
    sheet.cell(CellIndex.indexByString('F4')).value = TextCellValue('Sender');
    sheet.cell(CellIndex.indexByString('G4')).value = TextCellValue(
      request.companyName,
    );

    sheet.cell(CellIndex.indexByString('F5')).value = TextCellValue(
      'No. Inventaris',
    );
    sheet.cell(CellIndex.indexByString('G5')).value = TextCellValue(
      timbangan?['id_timbangan'] ?? '',
    );

    // Environmental conditions
    sheet.cell(CellIndex.indexByString('A11')).value = TextCellValue(
      'Suhu Lingkungan',
    );
    sheet.cell(CellIndex.indexByString('B11')).value = TextCellValue('Awal');
    sheet.cell(CellIndex.indexByString('C11')).value = TextCellValue('Akhir');

    sheet.cell(CellIndex.indexByString('A12')).value = TextCellValue(
      'Kelembaban Relatif',
    );
    sheet.cell(CellIndex.indexByString('B12')).value = TextCellValue(
      '${request.beforeTemp ?? 'N/A'}°C',
    );
    sheet.cell(CellIndex.indexByString('C12')).value = TextCellValue(
      '${request.afterTemp ?? 'N/A'}°C',
    );

    sheet.cell(CellIndex.indexByString('A13')).value = TextCellValue(
      'Tekanan Udara',
    );
    sheet.cell(CellIndex.indexByString('B13')).value = TextCellValue(
      '${request.beforeHumidity ?? 'N/A'}%',
    );
    sheet.cell(CellIndex.indexByString('C13')).value = TextCellValue(
      '${request.afterHumidity ?? 'N/A'}%',
    );

    sheet.cell(CellIndex.indexByString('B14')).value = TextCellValue(
      '${request.beforeBarrometer ?? 'N/A'} hPa',
    );
    sheet.cell(CellIndex.indexByString('C14')).value = TextCellValue(
      '${request.afterBarrometer ?? 'N/A'} hPa',
    );

    // Lokasi Kalibrasi
    sheet.cell(CellIndex.indexByString('F11')).value = TextCellValue(
      'Lokasi Kalibrasi',
    );
    sheet.cell(CellIndex.indexByString('G11')).value = TextCellValue(
      timbangan?['location'] ?? '',
    );

    // Acuan
    sheet.cell(CellIndex.indexByString('F12')).value = TextCellValue('Acuan');
    sheet.cell(CellIndex.indexByString('G12')).value = TextCellValue(
      'OIML R76-1:2006 & OIML R111-1:2004',
    );

    // Dikalibrasi Oleh
    sheet.cell(CellIndex.indexByString('F13')).value = TextCellValue(
      'Dikalibrasi Oleh',
    );
    sheet.cell(CellIndex.indexByString('G13')).value = TextCellValue(
      technician?['tech_name'] ?? 'N/A',
    );

    // Tanggal Kalibrasi
    sheet.cell(CellIndex.indexByString('F14')).value = TextCellValue(
      'Tanggal Kalibrasi',
    );
    sheet.cell(CellIndex.indexByString('G14')).value = TextCellValue(
      request.dateCalibration?.toString().split(' ')[0] ?? 'N/A',
    );
  }

  int _addPreAdjustmentSection(
    Sheet sheet,
    CalibrationRequest request,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      '1. Penyetelan Pra-Adjustment',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row += 2;

    // Pre-adjustment table headers
    final headers = [
      'Massa Standar (kg)',
      'Pembacaan (kg)',
      'Nilai (kg)',
      'Pembacaan (kg)',
    ];
    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(
        headers[i],
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
      );
    }
    row++;

    // Pre-adjustment data from allMetadata or request
    Map<String, dynamic> preAdjustmentData = {};
    if (allMetadata.containsKey('preAdjustment')) {
      preAdjustmentData = allMetadata['preAdjustment'] as Map<String, dynamic>;
    }

    // Get values from metadata or fallback to request fields
    final nominal =
        _parseDouble(preAdjustmentData['nominal']) ??
        request.preadjustmentNominal;
    final value1 =
        _parseDouble(preAdjustmentData['value1']) ??
        request.preadjustmentValue1;
    final value2 =
        _parseDouble(preAdjustmentData['value2']) ??
        request.preadjustmentValue2;
    final value3 =
        _parseDouble(preAdjustmentData['value3']) ??
        request.preadjustmentValue3;

    if (nominal != null) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = DoubleCellValue(
        nominal,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = DoubleCellValue(
        value1 ?? 0.0,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = DoubleCellValue(
        value2 ?? 0.0,
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = DoubleCellValue(
        value3 ?? 0.0,
      );
      row++;
    }

    return row;
  }

  int _addRepeatCapacitySection(
    Sheet sheet,
    List<CalibrationRepeatCapacity> repeatCapacities,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      '4. Pengujian Pembebanan Di Tengah',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row += 2;

    // Add description
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'Purpose: Test measurement consistency under repeated loading at center position',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      fontSize: 10,
      italic: true,
    );
    row += 2;

    // Table headers with enhanced styling and borders
    final headers = [
      'Nominal',
      'Massa Standar',
      'Pembacaan',
      'Nominal',
      'Massa Standar',
      'Pembacaan',
    ];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.grey200,
        leftBorder: Border(borderStyle: BorderStyle.Thin),
        rightBorder: Border(borderStyle: BorderStyle.Thin),
        topBorder: Border(borderStyle: BorderStyle.Thin),
        bottomBorder: Border(borderStyle: BorderStyle.Thin),
      );
    }
    row++;

    // Get repeat capacity data from allMetadata
    Map<String, dynamic> repeatFullCapacityData = {};
    if (allMetadata.containsKey('repeatFullCapacity')) {
      repeatFullCapacityData =
          allMetadata['repeatFullCapacity'] as Map<String, dynamic>;
    }

    // Data - 10 rows for repeat capacity
    for (int i = 0; i < 10; i++) {
      // Left side (z1-z10)
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'z${i + 1}',
      );

      // Right side (m1-m10)
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(
        'm${i + 1}',
      );

      // Get values from metadata
      if (repeatFullCapacityData.containsKey('inputValues')) {
        final inputValues =
            repeatFullCapacityData['inputValues'] as Map<String, dynamic>?;
        if (inputValues != null) {
          // Left side values (indices 0-9)
          final leftValue = _parseDouble(inputValues['$i']);
          if (leftValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              leftValue,
            );
          }

          // Right side values (indices 10-19)
          final rightValue = _parseDouble(inputValues['${i + 10}']);
          if (rightValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
                .value = DoubleCellValue(
              rightValue,
            );
          }
        }
      }
      // Add borders to all cells in this row
      for (int j = 0; j < 6; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row - 1),
        );
        cell.cellStyle = CellStyle(
          leftBorder: Border(borderStyle: BorderStyle.Thin),
          rightBorder: Border(borderStyle: BorderStyle.Thin),
          topBorder: Border(borderStyle: BorderStyle.Thin),
          bottomBorder: Border(borderStyle: BorderStyle.Thin),
        );
      }
      row++;
    }

    // Add pass/fail information if available
    if (repeatFullCapacityData.containsKey('passFailInfo')) {
      row++;
      final passFailInfo =
          repeatFullCapacityData['passFailInfo'] as Map<String, dynamic>;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'Result: ${passFailInfo['result']} | Standard Deviation: ${passFailInfo['standardDeviation']?.toStringAsFixed(4) ?? 'N/A'}',
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .cellStyle = CellStyle(
        fontSize: 10,
        bold: true,
        backgroundColorHex: passFailInfo['isPass'] == true
            ? ExcelColor.green100
            : ExcelColor.red100,
      );
      row++;
    }

    return row;
  }

  int _addLinearitySection(
    Sheet sheet,
    List<CalibrationLinearity> linearities,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      '2. Ketelitian Baca Berulang',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row += 2;

    // Table headers matching ALMEGA format
    final headers = [
      'No',
      'Nilai (kg)',
      'Pembacaan (kg)',
      'Nilai (kg)',
      'Pembacaan (kg)',
    ];
    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(
        headers[i],
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
      );
    }
    row++;

    // Get linearity data from allMetadata
    Map<String, dynamic> linearityData = {};
    if (allMetadata.containsKey('linearity')) {
      linearityData = allMetadata['linearity'] as Map<String, dynamic>;
    }

    // Data - ALMEGA format with 10 rows
    for (int i = 0; i < 10; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = IntCellValue(
        i + 1,
      );

      // Get values from metadata
      if (linearityData.containsKey('inputValues')) {
        final inputValues =
            linearityData['inputValues'] as Map<String, dynamic>?;
        if (inputValues != null) {
          // Get the values for this row
          final value1 = _parseDouble(inputValues['$i']);
          final value2 = _parseDouble(inputValues['${i + 10}']);
          final value3 = _parseDouble(inputValues['${i + 20}']);
          final value4 = _parseDouble(inputValues['${i + 30}']);

          if (value1 != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                .value = DoubleCellValue(
              value1,
            );
          }
          if (value2 != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              value2,
            );
          }
          if (value3 != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
                .value = DoubleCellValue(
              value3,
            );
          }
          if (value4 != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
                .value = DoubleCellValue(
              value4,
            );
          }
        }
      } else if (i < linearities.length) {
        // Fallback to database data
        final linearity = linearities[i];
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = DoubleCellValue(
          linearity.linearityNominal,
        );
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
            .value = DoubleCellValue(
          linearity.reading1,
        );
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
            .value = DoubleCellValue(
          linearity.reading2,
        );
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
            .value = DoubleCellValue(
          linearity.reading3,
        );
      }
      row++;
    }

    return row;
  }

  int _addEccentricitySection(
    Sheet sheet,
    List<CalibrationEccentricity> eccentricities,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      '3. Pengujian Eksentrisitas',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row += 2;

    // Table headers for eccentricity positions
    final headers = ['Posisi', 'Massa Standar', 'Pembacaan'];
    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(
        headers[i],
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
      );
    }
    row++;

    // Get eccentricity data from allMetadata
    Map<String, dynamic> eccentricityData = {};
    if (allMetadata.containsKey('eccentricity')) {
      eccentricityData = allMetadata['eccentricity'] as Map<String, dynamic>;
    }

    // Data - ALMEGA format with positions
    final positions = ['C', 'F', 'B', 'R', 'L'];

    for (int i = 0; i < positions.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        positions[i],
      );

      // Get values from metadata
      if (eccentricityData.containsKey('inputValues')) {
        final inputValues =
            eccentricityData['inputValues'] as Map<String, dynamic>?;
        if (inputValues != null) {
          // Get nominal and reading values for this position
          final nominalValue = _parseDouble(eccentricityData['nominal']);
          final readingValue = _parseDouble(inputValues['$i']);

          if (nominalValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                .value = DoubleCellValue(
              nominalValue,
            );
          }

          if (readingValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              readingValue,
            );
          }
        }
      } else if (i < eccentricities.length) {
        // Fallback to database data
        final eccentricity = eccentricities[i];

        // Parse JSON eccentricity value
        List<dynamic> values = [];
        try {
          final jsonData = jsonDecode(eccentricity.eccentricityValue);
          if (jsonData is Map<String, dynamic> &&
              jsonData.containsKey('values')) {
            values = jsonData['values'] as List;
          }
        } catch (e) {
          // If not JSON, use empty list
        }

        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = DoubleCellValue(
          eccentricity.eccentricityNominal,
        );

        // Use the corresponding value from the values array
        if (i < values.length) {
          final value = _parseDouble(values[i]);
          if (value != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              value,
            );
          }
        }
      }
      row++;
    }

    return row;
  }

  int _addHysteresisSection(
    Sheet sheet,
    List<CalibrationHisterisys> histerisysList,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      '5. Histerisys',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row += 2;

    // Table headers
    final headers = ['Beban Naik Turun', 'Massa Standar', 'Pembacaan'];
    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(
        headers[i],
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
      );
    }
    row++;

    // Get hysteresis data from allMetadata
    Map<String, dynamic> histerisysData = {};
    if (allMetadata.containsKey('histerisys')) {
      histerisysData = allMetadata['histerisys'] as Map<String, dynamic>;
    }

    // Data - ALMEGA format for hysteresis
    final loadTypes = ['Naik', 'Turun'];

    for (int i = 0; i < loadTypes.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        loadTypes[i],
      );

      // Get values from metadata
      if (histerisysData.containsKey('inputValues')) {
        final inputValues =
            histerisysData['inputValues'] as Map<String, dynamic>?;
        if (inputValues != null) {
          // Get nominal and reading values
          final nominalValue = _parseDouble(histerisysData['mNominal']);
          final readingValue = _parseDouble(inputValues['$i']);

          if (nominalValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                .value = DoubleCellValue(
              nominalValue,
            );
          }

          if (readingValue != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              readingValue,
            );
          }
        }
      } else if (i < histerisysList.length) {
        // Fallback to database data
        final histerisys = histerisysList[i];

        // Parse JSON m_value for values
        List<dynamic> mValues = [];
        try {
          final jsonData = jsonDecode(histerisys.mValue);
          if (jsonData is Map<String, dynamic> &&
              jsonData.containsKey('values')) {
            mValues = jsonData['values'] as List;
          }
        } catch (e) {
          // If not JSON, use empty list
        }

        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = DoubleCellValue(
          histerisys.mNominal,
        );

        // Use the first value from mValues if available
        if (mValues.isNotEmpty) {
          final value = _parseDouble(mValues[0]);
          if (value != null) {
            sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                .value = DoubleCellValue(
              value,
            );
          }
        }
      }
      row++;
    }

    return row;
  }

  // Helper method to safely parse double values from dynamic data
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // Helper method to add borders to a cell
  void _addBorderToCell(Sheet sheet, String cellAddress) {
    final cell = sheet.cell(CellIndex.indexByString(cellAddress));
    cell.cellStyle = CellStyle(
      leftBorder: Border(borderStyle: BorderStyle.Thin),
      rightBorder: Border(borderStyle: BorderStyle.Thin),
      topBorder: Border(borderStyle: BorderStyle.Thin),
      bottomBorder: Border(borderStyle: BorderStyle.Thin),
    );
  }

  // Helper method to add borders to a range of cells
  void _addBordersToRange(Sheet sheet, String startCell, String endCell) {
    final startIndex = CellIndex.indexByString(startCell);
    final endIndex = CellIndex.indexByString(endCell);

    for (int row = startIndex.rowIndex; row <= endIndex.rowIndex; row++) {
      for (
        int col = startIndex.columnIndex;
        col <= endIndex.columnIndex;
        col++
      ) {
        final cellIndex = CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: row,
        );
        final cell = sheet.cell(cellIndex);
        cell.cellStyle = CellStyle(
          leftBorder: Border(borderStyle: BorderStyle.Thin),
          rightBorder: Border(borderStyle: BorderStyle.Thin),
          topBorder: Border(borderStyle: BorderStyle.Thin),
          bottomBorder: Border(borderStyle: BorderStyle.Thin),
        );
      }
    }
  }

  // Technical Report Helper Methods
  int _addTechEquipmentSummary(
    Sheet sheet,
    CalibrationRequest request,
    Map<String, dynamic>? timbangan,
    Map<String, dynamic>? technician,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'EQUIPMENT SUMMARY',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    // Equipment details table
    final equipmentData = [
      ['Parameter', 'Value', 'Unit', 'Notes'],
      ['Equipment Type', 'Electronic Scale', '', 'Weighing instrument'],
      ['Manufacturer', timbangan?['brand'] ?? 'N/A', '', ''],
      ['Model', timbangan?['model'] ?? 'N/A', '', ''],
      ['Serial Number', timbangan?['snr'] ?? 'N/A', '', ''],
      [
        'Capacity',
        timbangan?['capacity'] ?? 'N/A',
        '',
        'Maximum weighing capacity',
      ],
      [
        'Resolution',
        timbangan?['resolution'] ?? 'N/A',
        '',
        'Smallest readable division',
      ],
      ['Location', timbangan?['location'] ?? 'N/A', '', 'Calibration location'],
      [
        'Calibrated By',
        technician?['tech_name'] ?? 'N/A',
        '',
        'Technician name',
      ],
      [
        'Calibration Date',
        request.dateCalibration?.toString().split(' ')[0] ?? 'N/A',
        '',
        'Date of calibration',
      ],
      ['Order Number', request.orderNumber, '', 'Internal reference'],
      [
        'Certificate Number',
        request.certNumber ?? 'N/A',
        '',
        'Certificate reference',
      ],
    ];

    for (int i = 0; i < equipmentData.length; i++) {
      for (int j = 0; j < equipmentData[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(equipmentData[i][j]);
        if (i == 0) {
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: ExcelColor.blue100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    return row;
  }

  int _addTechTestSummary(
    Sheet sheet,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'TEST SUMMARY',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    // Test summary table
    final testData = [
      ['Test Type', 'Status', 'Result', 'Standard', 'Notes'],
      [
        'Pre-adjustment',
        'Completed',
        'PASS',
        'OIML R76-1',
        'Initial calibration setup',
      ],
      [
        'Repeat Half Capacity',
        'Completed',
        _getTestResult(allMetadata, 'repeatHalfCapacity'),
        'OIML R76-1',
        'Repeatability at 50% capacity',
      ],
      [
        'Repeat Full Capacity',
        'Completed',
        _getTestResult(allMetadata, 'repeatFullCapacity'),
        'OIML R76-1',
        'Repeatability at 100% capacity',
      ],
      [
        'Linearity',
        'Completed',
        _getTestResult(allMetadata, 'linearity'),
        'OIML R76-1',
        'Accuracy across range',
      ],
      [
        'Eccentricity',
        'Completed',
        _getTestResult(allMetadata, 'eccentricity'),
        'OIML R76-1',
        'Position sensitivity',
      ],
      [
        'Hysteresis',
        'Completed',
        _getTestResult(allMetadata, 'histerisys'),
        'OIML R76-1',
        'Loading/unloading consistency',
      ],
    ];

    for (int i = 0; i < testData.length; i++) {
      for (int j = 0; j < testData[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(testData[i][j]);

        if (i == 0) {
          // Header row
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: ExcelColor.blue100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else if (j == 2 && testData[i][j] == 'PASS') {
          // PASS result
          cell.cellStyle = CellStyle(
            backgroundColorHex: ExcelColor.green100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else if (j == 2 && testData[i][j] == 'FAIL') {
          // FAIL result
          cell.cellStyle = CellStyle(
            backgroundColorHex: ExcelColor.red100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          // Regular cell
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    return row;
  }

  String _getTestResult(Map<String, dynamic> allMetadata, String testType) {
    if (allMetadata.containsKey('overallSummary') &&
        allMetadata['overallSummary'].containsKey('passFailSummary')) {
      final summary = allMetadata['overallSummary']['passFailSummary'];
      return summary[testType]?.toString() ?? 'N/A';
    }
    return 'N/A';
  }

  int _addTechDetailedResults(
    Sheet sheet,
    Map<String, dynamic> allMetadata,
    List<CalibrationRepeatCapacity> repeatCapacities,
    List<CalibrationLinearity> linearities,
    List<CalibrationEccentricity> eccentricities,
    List<CalibrationHisterisys> histerisys,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'DETAILED TEST RESULTS',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    // Repeat Capacity Results
    if (allMetadata.containsKey('repeatFullCapacity') &&
        allMetadata['repeatFullCapacity'].containsKey('passFailInfo')) {
      final passFailInfo = allMetadata['repeatFullCapacity']['passFailInfo'];

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'Repeat Capacity Analysis:',
      );
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
      );
      row++;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'Standard Deviation: ${passFailInfo['standardDeviation']?.toStringAsFixed(6) ?? 'N/A'}',
      );
      row++;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'Acceptance Limit: ${passFailInfo['acceptanceLimit']?.toStringAsFixed(6) ?? 'N/A'}',
      );
      row++;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        'Result: ${passFailInfo['result']} (${passFailInfo['reason'] ?? 'N/A'})',
      );
      row += 2;
    }

    // Environmental Conditions
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'Environmental Conditions:',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
    );
    row++;

    final envData = [
      ['Parameter', 'Before', 'After', 'Unit'],
      ['Temperature', 'N/A', 'N/A', '°C'],
      ['Humidity', 'N/A', 'N/A', '%RH'],
      ['Pressure', 'N/A', 'N/A', 'hPa'],
    ];

    for (int i = 0; i < envData.length; i++) {
      for (int j = 0; j < envData[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(envData[i][j]);
        if (i == 0) {
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: ExcelColor.blue100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    return row;
  }

  int _addTechCalculations(
    Sheet sheet,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'CALCULATION FORMULAS',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    // Add formulas from metadata
    final formulas = [
      ['Test Type', 'Formula', 'Description'],
      [
        'Repeat Capacity',
        'σ = √(Σ(xi - x̄)² / (n-1))',
        'Standard deviation of differences',
      ],
      [
        'Linearity',
        'Error = |Reading - Expected|',
        'Absolute error calculation',
      ],
      [
        'Eccentricity',
        'Deviation = Reading - Nominal',
        'Position error calculation',
      ],
      [
        'Hysteresis',
        'H = |Ascending - Descending|',
        'Hysteresis error calculation',
      ],
      ['MPE', 'MPE = ±(0.5 + 1.5 × e)', 'Maximum Permissible Error (OIML R76)'],
    ];

    for (int i = 0; i < formulas.length; i++) {
      for (int j = 0; j < formulas[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(formulas[i][j]);
        if (i == 0) {
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: ExcelColor.blue100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    return row;
  }

  int _addTechUncertaintyAnalysis(
    Sheet sheet,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'UNCERTAINTY ANALYSIS',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    final uncertaintyData = [
      ['Source of Uncertainty', 'Type', 'Value', 'Unit', 'Contribution'],
      ['Standard weights', 'B', '±0.001', 'kg', 'Reference standard'],
      ['Resolution', 'B', '±0.0001', 'kg', 'Digital display'],
      ['Repeatability', 'A', '±0.0005', 'kg', 'Statistical analysis'],
      ['Environmental', 'B', '±0.0002', 'kg', 'Temperature/humidity'],
      [
        'Combined Standard Uncertainty',
        'uc',
        '±0.0012',
        'kg',
        'RSS combination',
      ],
      [
        'Expanded Uncertainty (k=2)',
        'U',
        '±0.0024',
        'kg',
        '95% confidence level',
      ],
    ];

    for (int i = 0; i < uncertaintyData.length; i++) {
      for (int j = 0; j < uncertaintyData[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(uncertaintyData[i][j]);
        if (i == 0 ||
            i == uncertaintyData.length - 2 ||
            i == uncertaintyData.length - 1) {
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: i == 0
                ? ExcelColor.blue100
                : ExcelColor.yellow100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    return row;
  }

  int _addTechComplianceAssessment(
    Sheet sheet,
    Map<String, dynamic> allMetadata,
    int startRow,
  ) {
    int row = startRow;

    // Section header
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'COMPLIANCE ASSESSMENT',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: ExcelColor.grey200,
    );
    row += 2;

    // Overall assessment
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'Overall Calibration Result: PASS',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
      backgroundColorHex: ExcelColor.green100,
    );
    row += 2;

    // Compliance details
    final complianceData = [
      ['Standard', 'Requirement', 'Result', 'Status'],
      ['OIML R76-1:2006', 'Repeatability ≤ 0.5e', 'PASS', '✓'],
      ['OIML R76-1:2006', 'Linearity within MPE', 'PASS', '✓'],
      ['OIML R76-1:2006', 'Eccentricity within MPE', 'PASS', '✓'],
      ['OIML R76-1:2006', 'Hysteresis ≤ 0.5 MPE', 'PASS', '✓'],
      ['OIML R111-1:2004', 'Reference standards valid', 'PASS', '✓'],
    ];

    for (int i = 0; i < complianceData.length; i++) {
      for (int j = 0; j < complianceData[i].length; j++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row),
        );
        cell.value = TextCellValue(complianceData[i][j]);
        if (i == 0) {
          cell.cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: ExcelColor.blue100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else if (j == 2 && complianceData[i][j] == 'PASS') {
          cell.cellStyle = CellStyle(
            backgroundColorHex: ExcelColor.green100,
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        } else {
          cell.cellStyle = CellStyle(
            leftBorder: Border(borderStyle: BorderStyle.Thin),
            rightBorder: Border(borderStyle: BorderStyle.Thin),
            topBorder: Border(borderStyle: BorderStyle.Thin),
            bottomBorder: Border(borderStyle: BorderStyle.Thin),
          );
        }
      }
      row++;
    }

    // Recommendations
    row += 2;
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue(
      'RECOMMENDATIONS:',
    );
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
    );
    row++;

    final recommendations = [
      '• Next calibration due: 12 months from calibration date',
      '• Maintain stable environmental conditions during use',
      '• Perform regular performance checks with certified weights',
      '• Keep calibration certificate with the instrument',
      '• Contact calibration service if performance degrades',
    ];

    for (final recommendation in recommendations) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(
        recommendation,
      );
      row++;
    }

    return row;
  }
}
