import 'package:ekalibrasi/helper/model.dart';
import 'package:ekalibrasi/repositories/CalibrationRepository.dart';
import 'package:ekalibrasi/screens/CalibrationRequestAdd.dart';
import 'package:ekalibrasi/screens/CalibrationRequestDetail.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:uuid/uuid.dart';

class CalibrationRequestsList extends StatefulWidget {
  final CalibrationRepository calibrationRepository;
  final VoidCallback? onLogout;
  const CalibrationRequestsList({
    super.key,
    required this.calibrationRepository,
    this.onLogout,
  });

  @override
  State<CalibrationRequestsList> createState() =>
      _CalibrationRequestsListState();
}

class _CalibrationRequestsListState extends State<CalibrationRequestsList> {
  List<CalibrationRequest> requests = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  Future<void> _loadRequests() async {
    setState(() => isLoading = true);
    try {
      final loadedRequests = await widget.calibrationRepository
          .getCalibrationRequestsLocal();
      setState(() {
        requests = loadedRequests;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load requests: $e')));
      }
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onLogout?.call();
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  List<CalibrationRequest> _mockRequests() {
    return [
      CalibrationRequest(
        id: 1,
        uuid: const Uuid().v4(),
        orderNumber: 'ORD-001',
        companyName: 'PT Example 1',
        companyAddress: 'Jl. Example No.1',
        contactPerson: 'John Doe',
        cpDivision: 'Quality Control',
        certNumber: 'CERT-001',
        dateCalibration: DateTime.now(),
        createdAt: DateTime.now(),
      ),
      CalibrationRequest(
        id: 2,
        uuid: const Uuid().v4(),
        orderNumber: 'ORD-002',
        companyName: 'PT Example 2',
        companyAddress: 'Jl. Example No.2',
        contactPerson: 'Jane Smith',
        cpDivision: 'Production',
        certNumber: 'CERT-002',
        dateCalibration: DateTime.now().add(const Duration(days: 1)),
        createdAt: DateTime.now(),
      ),
    ];
  }

  final frameworks = {
    'next': 'Next.js',
    'react': 'React',
    'astro': 'Astro',
    'nuxt': 'Nuxt.js',
  };

  final branches = {
    '00': 'Bandung',
    '01': 'Jakarta',
    '02': 'Semarang',
    '03': 'Surabaya',
    '04': 'Lab_Kalibrasi',
  };

  final anakTimbang = {
    'AT.00.02': 'AT.00.02',
    'AT.00.03': 'AT.00.03',
    'AT.00.04': 'AT.00.04',
    'AT.00.05': 'AT.00.05',
  };

  final eccentricity = [
    {'label': 'C'},
    {'label': 'F'},
    {'label': 'B'},
    {'label': 'R'},
    {'label': 'L'},
  ];

  final histerisys = [
    {'label': 'M(p1)'},
    {'label': 'M+M\''},
    {'label': 'M(q1)'},
    {'label': 'Zero'},
    {'label': 'M+M\''},
    {'label': 'M(q2)'},
    {'label': 'Zero'},
    {'label': 'M(p2)'},
    {'label': 'M(p3)'},
    {'label': 'M+M\''},
    {'label': 'M(q3)'},
    {'label': 'Zero'},
    {'label': 'M+M\''},
    {'label': 'M(q4)'},
    {'label': 'Zero'},
    {'label': 'M(p4)'},
  ];

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calibration Requests'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadRequests),
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () async {
              try {
                await _loadRequests();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sync completed successfully'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(SnackBar(content: Text('Sync failed: $e')));
                }
              }
            },
          ),
          if (widget.onLogout != null)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'logout') {
                  _showLogoutDialog();
                }
              },
              itemBuilder: (BuildContext context) => [
                const PopupMenuItem<String>(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout),
                      SizedBox(width: 8),
                      Text('Logout'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddRequest(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (requests.isEmpty) {
      return const Center(child: Text('No calibration requests found'));
    }
    return ListView.builder(
      itemCount: requests.length,
      itemBuilder: (context, index) {
        final request = requests[index];
        return Card(
          margin: const EdgeInsets.all(8.0),
          child: ListTile(
            title: Text(request.orderNumber),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(request.companyName),
                Text(
                  DateFormat(
                    'dd MMM yyyy',
                  ).format(request.dateCalibration ?? request.createdAt),
                ),
                Text('Status: ${request.status}'),
              ],
            ),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _navigateToRequestDetail(request),
          ),
        );
      },
    );
  }

  Future<void> _navigateToAddRequest() async {
    final newRequest = await Navigator.push<CalibrationRequest>(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddCalibrationRequest(repository: widget.calibrationRepository),
      ),
    );

    if (newRequest != null) {
      setState(() {
        requests.insert(0, newRequest);
      });
    }
  }

  Future<void> _navigateToRequestDetail(CalibrationRequest request) async {
    final updatedRequest = await Navigator.push<CalibrationRequest>(
      context,
      MaterialPageRoute(
        builder: (context) => CalibrationRequestDetail(
          request: request,
          repository: widget.calibrationRepository,
        ),
      ),
    );

    if (updatedRequest != null) {
      setState(() {
        final index = requests.indexWhere((r) => r.id == updatedRequest.id);
        if (index != -1) {
          requests[index] = updatedRequest;
        }
      });
    }
  }
}

const invoices = [
  (
    invoice: "INV001",
    paymentStatus: "Paid",
    totalAmount: r"$250.00",
    paymentMethod: "Credit Card",
  ),
  (
    invoice: "INV002",
    paymentStatus: "Pending",
    totalAmount: r"$150.00",
    paymentMethod: "PayPal",
  ),
  (
    invoice: "INV003",
    paymentStatus: "Unpaid",
    totalAmount: r"$350.00",
    paymentMethod: "Bank Transfer",
  ),
  (
    invoice: "INV004",
    paymentStatus: "Paid",
    totalAmount: r"$450.00",
    paymentMethod: "Credit Card",
  ),
  (
    invoice: "INV005",
    paymentStatus: "Paid",
    totalAmount: r"$550.00",
    paymentMethod: "PayPal",
  ),
  (
    invoice: "INV006",
    paymentStatus: "Pending",
    totalAmount: r"$200.00",
    paymentMethod: "Bank Transfer",
  ),
  (
    invoice: "INV007",
    paymentStatus: "Unpaid",
    totalAmount: r"$300.00",
    paymentMethod: "Credit Card",
  ),
];

class TablePage extends StatelessWidget {
  const TablePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 600,
            // added just to center the table vertically
            maxHeight: 450,
          ),
          child: ShadTable.list(
            header: const [
              ShadTableCell.header(child: Text('Invoice')),
              ShadTableCell.header(child: Text('Status')),
              ShadTableCell.header(child: Text('Method')),
              ShadTableCell.header(
                alignment: Alignment.centerRight,
                child: Text('Amount'),
              ),
            ],
            footer: const [
              ShadTableCell.footer(child: Text('Total')),
              ShadTableCell.footer(child: Text('')),
              ShadTableCell.footer(child: Text('')),
              ShadTableCell.footer(
                alignment: Alignment.centerRight,
                child: Text(r'$2500.00'),
              ),
            ],
            columnSpanExtent: (index) {
              if (index == 2) return const FixedTableSpanExtent(130);
              if (index == 3) {
                return const MaxTableSpanExtent(
                  FixedTableSpanExtent(120),
                  RemainingTableSpanExtent(),
                );
              }
              // uses the default value
              return null;
            },
            children: invoices.map(
              (invoice) => [
                ShadTableCell(
                  child: Text(
                    invoice.invoice,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
                ShadTableCell(child: Text(invoice.paymentStatus)),
                ShadTableCell(child: Text(invoice.paymentMethod)),
                ShadTableCell(
                  alignment: Alignment.centerRight,
                  child: Text(invoice.totalAmount),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
