import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

// Define the repeat capacity labels
final repeatCapacityZ = [
  {'label': 'Z₁'},
  {'label': 'Z₂'},
  {'label': 'Z₃'},
  {'label': 'Z₄'},
  {'label': 'Z₅'},
  {'label': 'Z₆'},
  {'label': 'Z₇'},
  {'label': 'Z₈'},
  {'label': 'Z₉'},
  {'label': 'Z₁₀'},
];

final repeatCapacityM = [
  {'label': 'M₁'},
  {'label': 'M₂'},
  {'label': 'M₃'},
  {'label': 'M₄'},
  {'label': 'M₅'},
  {'label': 'M₆'},
  {'label': 'M₇'},
  {'label': 'M₈'},
  {'label': 'M₉'},
  {'label': 'M₁₀'},
];

class RepeatCapacityCard extends StatefulWidget {
  final String title;
  final bool isMobile;
  final String halfNominalValue;
  final String fullNominalValue;
  final Function(String) onHalfNominalChanged;
  final Function(String) onFullNominalChanged;
  final List<TextEditingController> valueControllers1;
  final List<TextEditingController> valueControllers2;
  final String selectedHalfDayaBaca;
  final String selectedFullDayaBaca;
  final Function(String) onHalfDayaBacaChanged;
  final Function(String) onFullDayaBacaChanged;
  final String dayaBacaR1Value;
  final String dayaBacaR2Value;
  final String dayaBacaR3Value;
  final String unitText;
  final Function() onRemove;
  final bool showRemoveButton;

  const RepeatCapacityCard({
    super.key,
    required this.title,
    required this.isMobile,
    required this.halfNominalValue,
    required this.fullNominalValue,
    required this.onHalfNominalChanged,
    required this.onFullNominalChanged,
    required this.valueControllers1,
    required this.valueControllers2,
    required this.selectedHalfDayaBaca,
    required this.selectedFullDayaBaca,
    required this.onHalfDayaBacaChanged,
    required this.onFullDayaBacaChanged,
    required this.dayaBacaR1Value,
    required this.dayaBacaR2Value,
    required this.dayaBacaR3Value,
    required this.unitText,
    required this.onRemove,
    this.showRemoveButton = true,
  });

  @override
  State<RepeatCapacityCard> createState() => _RepeatCapacityCardState();
}

class _RepeatCapacityCardState extends State<RepeatCapacityCard> {
  late TextEditingController _halfNominalController;
  late TextEditingController _fullNominalController;

  @override
  void initState() {
    super.initState();
    _halfNominalController = TextEditingController(
      text: widget.halfNominalValue,
    );
    _fullNominalController = TextEditingController(
      text: widget.fullNominalValue,
    );

    _halfNominalController.addListener(() {
      widget.onHalfNominalChanged(_halfNominalController.text);
    });

    _fullNominalController.addListener(() {
      widget.onFullNominalChanged(_fullNominalController.text);
    });
  }

  @override
  void dispose() {
    _halfNominalController.dispose();
    _fullNominalController.dispose();
    super.dispose();
  }

  // Get decimal digits from daya baca value
  int _getDecimalDigits(String dayaBacaValue) {
    if (dayaBacaValue.isEmpty) return 0;
    if (!dayaBacaValue.contains('.')) return 0;
    final decimalPart = dayaBacaValue.split('.')[1];
    return decimalPart.length;
  }

  // Get the selected daya baca value for half capacity
  String _getSelectedHalfDayaBacaValue() {
    switch (widget.selectedHalfDayaBaca) {
      case '1':
        return widget.dayaBacaR1Value;
      case '2':
        return widget.dayaBacaR2Value;
      case '3':
        return widget.dayaBacaR3Value;
      default:
        return widget.dayaBacaR1Value;
    }
  }

  // Get the selected daya baca value for full capacity
  String _getSelectedFullDayaBacaValue() {
    switch (widget.selectedFullDayaBaca) {
      case '1':
        return widget.dayaBacaR1Value;
      case '2':
        return widget.dayaBacaR2Value;
      case '3':
        return widget.dayaBacaR3Value;
      default:
        return widget.dayaBacaR1Value;
    }
  }

  // Format the left yellow box value for half capacity
  String _formatHalfLeftYellowBoxValue(int index) {
    // First 10 indices (0-9) use 0 nominal, next 10 (10-19) use filled nominal
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_halfNominalController.text) ?? 0.0);
    final dayaBacaValue = _getSelectedHalfDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Format with the appropriate decimal places
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the left yellow box value for full capacity
  String _formatFullLeftYellowBoxValue(int index) {
    // First 10 indices (0-9) use 0 nominal, next 10 (10-19) use filled nominal
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_fullNominalController.text) ?? 0.0);
    final dayaBacaValue = _getSelectedFullDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Format with the appropriate decimal places
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the right yellow box value for half capacity
  String _formatHalfRightYellowBoxValue(int index) {
    final middleValueText = index < 10
        ? widget.valueControllers1[index].text
        : widget
              .valueControllers1[index]
              .text; // Half capacity uses valueControllers1

    // Default to left side value if no middle value
    if (middleValueText.isEmpty) {
      return _formatHalfLeftYellowBoxValue(index);
    }

    // Parse values
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_halfNominalController.text) ?? 0.0);
    final middleValue = double.tryParse(middleValueText) ?? 0.0;

    // Get decimal digits from selected daya baca
    final dayaBacaValue = _getSelectedHalfDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Calculate the minimum decimal addition
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    // Calculate: left side + (middle value * minimum decimal)
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);

    // Format with the appropriate decimal places
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the right yellow box value for full capacity
  String _formatFullRightYellowBoxValue(int index) {
    final middleValueText = index < 10
        ? widget.valueControllers2[index].text
        : widget
              .valueControllers2[index]
              .text; // Full capacity uses valueControllers2

    // Default to left side value if no middle value
    if (middleValueText.isEmpty) {
      return _formatFullLeftYellowBoxValue(index);
    }

    // Parse values
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_fullNominalController.text) ?? 0.0);
    final middleValue = double.tryParse(middleValueText) ?? 0.0;

    // Get decimal digits from selected daya baca
    final dayaBacaValue = _getSelectedFullDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Calculate the minimum decimal addition
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    // Calculate: left side + (middle value * minimum decimal)
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);

    // Format with the appropriate decimal places
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Helper method to parse right yellow box value
  double _parseRightYellowBoxValue(String formattedValue) {
    return double.tryParse(formattedValue.replaceAll(',', '.')) ?? 0.0;
  }

  // Helper method to calculate standard deviation
  double _calculateStandardDeviation(List<double> values) {
    if (values.isEmpty) return 0.0;

    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance =
        values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) /
        values.length;

    return math.sqrt(variance);
  }

  // Calculate PASS/FAIL for half capacity
  Map<String, dynamic> _calculateHalfPassFail() {
    List<double> differences = [];

    // Calculate differences for half capacity: right yellow box values of indices 10-19 minus 0-9
    for (int i = 0; i < 10; i++) {
      final rightValue0to9 = _parseRightYellowBoxValue(
        _formatHalfRightYellowBoxValue(i),
      );
      final rightValue10to19 = _parseRightYellowBoxValue(
        _formatHalfRightYellowBoxValue(i + 10),
      );
      differences.add(rightValue10to19 - rightValue0to9);
    }

    // Calculate standard deviation
    final standardDeviation = _calculateStandardDeviation(differences);

    // Get selected daya baca value for comparison
    final selectedDayaBacaValue =
        double.tryParse(_getSelectedHalfDayaBacaValue()) ?? 0.0;

    // Determine PASS/FAIL
    final isPass = standardDeviation <= selectedDayaBacaValue;

    return {
      'differences': differences,
      'standardDeviation': standardDeviation,
      'selectedDayaBacaValue': selectedDayaBacaValue,
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
    };
  }

  // Calculate PASS/FAIL for full capacity
  Map<String, dynamic> _calculateFullPassFail() {
    List<double> differences = [];

    // Calculate differences for full capacity: right yellow box values of indices 10-19 minus 0-9
    for (int i = 0; i < 10; i++) {
      final rightValue0to9 = _parseRightYellowBoxValue(
        _formatFullRightYellowBoxValue(i),
      );
      final rightValue10to19 = _parseRightYellowBoxValue(
        _formatFullRightYellowBoxValue(i + 10),
      );
      differences.add(rightValue10to19 - rightValue0to9);
    }

    // Calculate standard deviation
    final standardDeviation = _calculateStandardDeviation(differences);

    // Get selected daya baca value for comparison
    final selectedDayaBacaValue =
        double.tryParse(_getSelectedFullDayaBacaValue()) ?? 0.0;

    // Determine PASS/FAIL
    final isPass = standardDeviation <= selectedDayaBacaValue;

    return {
      'differences': differences,
      'standardDeviation': standardDeviation,
      'selectedDayaBacaValue': selectedDayaBacaValue,
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
    };
  }

  // Get PASS/FAIL result text for half capacity
  String _getHalfPassFailText() {
    final result = _calculateHalfPassFail();
    return result['result'];
  }

  // Get PASS/FAIL color for half capacity
  Color _getHalfPassFailColor() {
    final result = _calculateHalfPassFail();
    return result['isPass'] ? Colors.green : Colors.red;
  }

  // Get PASS/FAIL result text for full capacity
  String _getFullPassFailText() {
    final result = _calculateFullPassFail();
    return result['result'];
  }

  // Get PASS/FAIL color for full capacity
  Color _getFullPassFailColor() {
    final result = _calculateFullPassFail();
    return result['isPass'] ? Colors.green : Colors.red;
  }

  // Build half capacity daya baca dropdown
  Widget _buildHalfDayaBacaDropdown() {
    return GestureDetector(
      onTap: () => _showHalfDayaBacaDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Daya Baca ${widget.selectedHalfDayaBaca}',
                style: const TextStyle(color: Colors.black),
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  // Build full capacity daya baca dropdown
  Widget _buildFullDayaBacaDropdown() {
    return GestureDetector(
      onTap: () => _showFullDayaBacaDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                'Daya Baca ${widget.selectedFullDayaBaca}',
                style: const TextStyle(color: Colors.black),
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  // Show half capacity daya baca selection dialog
  void _showHalfDayaBacaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Daya Baca - Half Capacity'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Column(
              children: [
                ListTile(
                  title: const Text('Daya Baca 1'),
                  trailing: widget.selectedHalfDayaBaca == '1'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onHalfDayaBacaChanged('1');
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 2'),
                  trailing: widget.selectedHalfDayaBaca == '2'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onHalfDayaBacaChanged('2');
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 3'),
                  trailing: widget.selectedHalfDayaBaca == '3'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onHalfDayaBacaChanged('3');
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  // Show full capacity daya baca selection dialog
  void _showFullDayaBacaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Daya Baca - Full Capacity'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Column(
              children: [
                ListTile(
                  title: const Text('Daya Baca 1'),
                  trailing: widget.selectedFullDayaBaca == '1'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onFullDayaBacaChanged('1');
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 2'),
                  trailing: widget.selectedFullDayaBaca == '2'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onFullDayaBacaChanged('2');
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 3'),
                  trailing: widget.selectedFullDayaBaca == '3'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    widget.onFullDayaBacaChanged('3');
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: const Color.fromARGB(255, 251, 147, 159),
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and remove button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.showRemoveButton)
                  IconButton(
                    onPressed: widget.onRemove,
                    icon: const Icon(Icons.delete, color: Colors.red),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // REPEAT Half Capacity Section
            Text(
              'REPEAT Half Capacity',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Half Capacity Nominal input
            const Text('Nominal'),
            const SizedBox(height: 6),
            SizedBox(
              width: 380.0,
              child: ShadInput(
                controller: _halfNominalController,
                placeholder: const Text('Enter nominal value'),
                trailing: Text(widget.unitText),
              ),
            ),
            const SizedBox(height: 16),

            // Half Capacity Daya Baca Selection
            const Text('Pilih Daya Baca'),
            const SizedBox(height: 6),
            SizedBox(width: 200.0, child: _buildHalfDayaBacaDropdown()),
            const SizedBox(height: 16),

            // Header row for half capacity
            Flex(
              direction: widget.isMobile ? Axis.vertical : Axis.horizontal,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 110.0,
                            child: Text("Nominal"),
                          ),
                          Container(
                            width: 100.0,
                            padding: EdgeInsets.all(8.0),
                            child: Text("Correction"),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.topRight,
                              padding: EdgeInsets.fromLTRB(8.0, 8.0, 16.0, 8.0),
                              child: Text("Reading"),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left side: indices 0-9
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: 10,
                              itemBuilder: (context, index) {
                                return Container(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          right: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10.0),
                                            bottomLeft: Radius.circular(10.0),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatHalfLeftYellowBoxValue(
                                                index,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: ShadInput(
                                          controller:
                                              widget.valueControllers1[index],
                                          placeholder: Text('0'),
                                        ),
                                      ),
                                      Container(
                                        width: 40.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                        ),
                                        child: Center(
                                          child: Text(
                                            repeatCapacityZ[index]['label']!,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          left: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(10.0),
                                            bottomRight: Radius.circular(10.0),
                                          ),
                                          border: Border(
                                            left: BorderSide(
                                              width: 1.0,
                                              color: const Color.fromARGB(
                                                255,
                                                197,
                                                195,
                                                195,
                                              ),
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatHalfRightYellowBoxValue(
                                                index,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                widget.isMobile
                    ? SizedBox(height: 16.0)
                    : SizedBox(width: 16.0),
                Flexible(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 110.0,
                            child: Text("Nominal"),
                          ),
                          Container(
                            width: 100.0,
                            padding: EdgeInsets.all(8.0),
                            child: Text("Correction"),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.topRight,
                              padding: EdgeInsets.fromLTRB(8.0, 8.0, 16.0, 8.0),
                              child: Text("Reading"),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Right side: indices 10-19
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: 10,
                              itemBuilder: (context, index) {
                                final actualIndex = index + 10;
                                return Container(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          right: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10.0),
                                            bottomLeft: Radius.circular(10.0),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatHalfLeftYellowBoxValue(
                                                actualIndex,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: ShadInput(
                                          controller: widget
                                              .valueControllers1[actualIndex],
                                          placeholder: Text('0'),
                                        ),
                                      ),
                                      Container(
                                        width: 40.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                        ),
                                        child: Center(
                                          child: Text(
                                            repeatCapacityM[index]['label']!,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          left: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(10.0),
                                            bottomRight: Radius.circular(10.0),
                                          ),
                                          border: Border(
                                            left: BorderSide(
                                              width: 1.0,
                                              color: const Color.fromARGB(
                                                255,
                                                197,
                                                195,
                                                195,
                                              ),
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatHalfRightYellowBoxValue(
                                                actualIndex,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Half Capacity Data Table (side by side: 0-9 and 10-19)
            const SizedBox(height: 16),

            // Half Capacity PASS/FAIL Result
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _getHalfPassFailColor() == Colors.green
                      ? [
                          Color.fromARGB(255, 0, 212, 71),
                          Color.fromARGB(255, 63, 181, 85),
                        ]
                      : [
                          Color.fromARGB(255, 212, 0, 0),
                          Color.fromARGB(255, 181, 63, 63),
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Half Capacity: ${_getHalfPassFailText()}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 32),

            // REPEAT Full Capacity Section
            Text(
              'REPEAT Full Capacity',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Full Capacity Nominal input
            const Text('Nominal'),
            const SizedBox(height: 6),
            SizedBox(
              width: 380.0,
              child: ShadInput(
                controller: _fullNominalController,
                placeholder: const Text('Enter nominal value'),
                trailing: Text(widget.unitText),
              ),
            ),
            const SizedBox(height: 16),

            // Full Capacity Daya Baca Selection
            const Text('Pilih Daya Baca'),
            const SizedBox(height: 6),
            SizedBox(width: 200.0, child: _buildFullDayaBacaDropdown()),
            const SizedBox(height: 16),

            // Header row for full capacity
            Flex(
              direction: widget.isMobile ? Axis.vertical : Axis.horizontal,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 110.0,
                            child: Text("Nominal"),
                          ),
                          Container(
                            width: 100.0,
                            padding: EdgeInsets.all(8.0),
                            child: Text("Correction"),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.topRight,
                              padding: EdgeInsets.fromLTRB(8.0, 8.0, 16.0, 8.0),
                              child: Text("Reading"),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left side: indices 0-9
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: 10,
                              itemBuilder: (context, index) {
                                return Container(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          right: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10.0),
                                            bottomLeft: Radius.circular(10.0),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatFullLeftYellowBoxValue(
                                                index,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: ShadInput(
                                          controller:
                                              widget.valueControllers2[index],
                                          placeholder: Text('${index + 1}'),
                                        ),
                                      ),
                                      Container(
                                        width: 40.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                        ),
                                        child: Center(
                                          child: Text(
                                            repeatCapacityZ[index]['label']!,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          left: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(10.0),
                                            bottomRight: Radius.circular(10.0),
                                          ),
                                          border: Border(
                                            left: BorderSide(
                                              width: 1.0,
                                              color: const Color.fromARGB(
                                                255,
                                                197,
                                                195,
                                                195,
                                              ),
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatFullRightYellowBoxValue(
                                                index,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                widget.isMobile
                    ? SizedBox(height: 16.0)
                    : SizedBox(width: 16.0),
                Flexible(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 110.0,
                            child: Text("Nominal"),
                          ),
                          Container(
                            width: 100.0,
                            padding: EdgeInsets.all(8.0),
                            child: Text("Correction"),
                          ),
                          Expanded(
                            child: Container(
                              alignment: Alignment.topRight,
                              padding: EdgeInsets.fromLTRB(8.0, 8.0, 16.0, 8.0),
                              child: Text("Reading"),
                            ),
                          ),
                        ],
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Right side: indices 10-19
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: 10,
                              itemBuilder: (context, index) {
                                final actualIndex = index + 10;
                                return Container(
                                  padding: EdgeInsets.only(bottom: 4.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          right: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10.0),
                                            bottomLeft: Radius.circular(10.0),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatFullLeftYellowBoxValue(
                                                actualIndex,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: ShadInput(
                                          controller: widget
                                              .valueControllers2[actualIndex],
                                          placeholder: Text(
                                            '${actualIndex + 1}',
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 40.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                        ),
                                        child: Center(
                                          child: Text(
                                            repeatCapacityM[index]['label']!,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 110.0,
                                        padding: EdgeInsets.only(
                                          top: 10.0,
                                          bottom: 10.0,
                                          left: 2.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.yellow,
                                          borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(10.0),
                                            bottomRight: Radius.circular(10.0),
                                          ),
                                          border: Border(
                                            left: BorderSide(
                                              width: 1.0,
                                              color: const Color.fromARGB(
                                                255,
                                                197,
                                                195,
                                                195,
                                              ),
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                          child: FittedBox(
                                            child: Text(
                                              _formatFullRightYellowBoxValue(
                                                actualIndex,
                                              ),
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Full Capacity Data Table (side by side: 0-9 and 10-19)
            const SizedBox(height: 16),

            // Full Capacity PASS/FAIL Result
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _getFullPassFailColor() == Colors.green
                      ? [
                          Color.fromARGB(255, 0, 212, 71),
                          Color.fromARGB(255, 63, 181, 85),
                        ]
                      : [
                          Color.fromARGB(255, 212, 0, 0),
                          Color.fromARGB(255, 181, 63, 63),
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Full Capacity: ${_getFullPassFailText()}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
