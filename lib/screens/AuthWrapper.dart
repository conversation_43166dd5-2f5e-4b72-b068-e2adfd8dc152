import 'package:flutter/material.dart';
import '../services/AuthService.dart';
import '../services/MasterDataSyncService.dart';
import '../helper/database.dart';
import '../repositories/CalibrationRepository.dart';
import '../services/SyncService.dart';
import 'LoginScreen.dart';
import 'CalibrationRequestList.dart';
import 'SyncScreen.dart';

class AuthWrapper extends StatefulWidget {
  final CalibrationRepository repository;
  final SyncService syncService;

  const AuthWrapper({
    super.key,
    required this.repository,
    required this.syncService,
  });

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late AuthService _authService;
  late MasterDataSyncService _syncService;
  bool _isLoading = true;
  bool _isLoggedIn = false;
  bool _needsSync = false;

  @override
  void initState() {
    super.initState();
    _authService = AuthService(
      dbHelper: DatabaseHelper.instance,
      apiBaseUrl: widget.repository.apiBaseUrl,
    );
    _syncService = MasterDataSyncService(
      dbHelper: DatabaseHelper.instance,
      apiBaseUrl: widget.repository.apiBaseUrl,
    );
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      bool needsSync = false;

      if (isLoggedIn) {
        // Check if sync is needed (token exists and internet available)
        needsSync = await _syncService.shouldSync();
      }

      setState(() {
        _isLoggedIn = isLoggedIn;
        _needsSync = needsSync;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoggedIn = false;
        _needsSync = false;
        _isLoading = false;
      });
    }
  }

  void _onLoginSuccess() {
    setState(() {
      _isLoggedIn = true;
      _needsSync = true; // Trigger sync after successful login
    });
  }

  void _onSyncComplete() {
    setState(() {
      _needsSync = false; // Sync completed, proceed to home
    });
  }

  void _onLogout() async {
    await _authService.logout();
    setState(() {
      _isLoggedIn = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_isLoggedIn) {
      // Show sync screen if sync is needed
      if (_needsSync) {
        return SyncScreen(
          repository: widget.repository,
          onSyncComplete: _onSyncComplete,
        );
      }

      // Show main app after sync is complete
      return CalibrationRequestsList(
        calibrationRepository: widget.repository,
        onLogout: _onLogout,
      );
    } else {
      return LoginScreenWrapper(
        onLoginSuccess: _onLoginSuccess,
        repository: widget.repository,
      );
    }
  }
}

class LoginScreenWrapper extends StatelessWidget {
  final VoidCallback onLoginSuccess;
  final CalibrationRepository repository;

  const LoginScreenWrapper({
    super.key,
    required this.onLoginSuccess,
    required this.repository,
  });

  @override
  Widget build(BuildContext context) {
    return Navigator(
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/home':
            // When login is successful, call the callback
            WidgetsBinding.instance.addPostFrameCallback((_) {
              onLoginSuccess();
            });
            return MaterialPageRoute(
              builder: (context) => const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              ),
            );
          default:
            return MaterialPageRoute(
              builder: (context) => LoginScreen(repository: repository),
            );
        }
      },
    );
  }
}
