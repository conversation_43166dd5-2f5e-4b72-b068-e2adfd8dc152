import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../helper/model.dart';
import '../repositories/CalibrationRepository.dart';

class PdfService {
  final CalibrationRepository repository;

  PdfService({required this.repository});

  Future<String> generateCalibrationPdf(String requestId) async {
    try {
      // Get calibration request data
      final request = await repository.getCalibrationRequestByUuid(requestId);
      if (request == null) {
        throw Exception('Calibration request not found');
      }

      // Get timbangan data if available
      Map<String, dynamic>? timbangan;
      if (request.timbanganUuid != null) {
        timbangan = await repository.getMasterTimbanganByUuid(
          request.timbanganUuid!,
        );
      }

      // Get technician data if available
      Map<String, dynamic>? technician;
      if (request.technicianUuid != null) {
        technician = await repository.getMasterTechnicianByUuid(
          request.technicianUuid!,
        );
      }

      // Parse allMetadata to get complete form data
      Map<String, dynamic> allMetadata = {};
      if (request.allMetadata != null && request.allMetadata!.isNotEmpty) {
        try {
          allMetadata = jsonDecode(request.allMetadata!);
        } catch (e) {
          print('Error parsing allMetadata: $e');
        }
      }

      // Get related data (for compatibility)
      final repeatCapacities = await repository.getRepeatCapacitiesForRequest(
        requestId,
      );
      final linearities = await repository.getLinearitiesForRequest(requestId);
      final eccentricities = await repository.getEccentricitiesForRequest(
        requestId,
      );
      final histerisysList = await repository.getHisterisysForRequest(
        requestId,
      );

      // Create PDF document
      final pdf = pw.Document();

      // Add pages
      pdf.addPage(
        _buildMainPage(
          request,
          timbangan,
          technician,
          allMetadata,
          repeatCapacities,
          linearities,
          eccentricities,
          histerisysList,
        ),
      );

      // Add additional pages if needed for large datasets
      if (repeatCapacities.length > 10 || linearities.length > 10) {
        pdf.addPage(
          _buildDataDetailsPage(
            repeatCapacities,
            linearities,
            eccentricities,
            histerisysList,
          ),
        );
      }

      // Save the PDF file
      final directory = await getApplicationDocumentsDirectory();

      // Create a subdirectory for PDF reports
      final reportsDir = Directory('${directory.path}/pdf_reports');
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      // Sanitize the order number for file name
      final sanitizedOrderNumber = request.orderNumber
          .replaceAll(RegExp(r'[<>:"/\\|?*\s]'), '_')
          .replaceAll(RegExp(r'_+'), '_');

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName =
          'Calibration_Report_${sanitizedOrderNumber}_$timestamp.pdf';
      final filePath = '${reportsDir.path}/$fileName';

      print('Attempting to save PDF file to: $filePath');
      print('Sanitized order number: $sanitizedOrderNumber');

      final file = File(filePath);
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      print('PDF file saved successfully to: $filePath');

      return filePath;
    } catch (e) {
      print('Error generating PDF: $e');
      throw Exception('Failed to generate PDF report: $e');
    }
  }

  pw.Page _buildMainPage(
    CalibrationRequest request,
    Map<String, dynamic>? timbangan,
    Map<String, dynamic>? technician,
    Map<String, dynamic> allMetadata,
    List<CalibrationRepeatCapacity> repeatCapacities,
    List<CalibrationLinearity> linearities,
    List<CalibrationEccentricity> eccentricities,
    List<CalibrationHisterisys> histerisysList,
  ) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            _buildAlmegaHeader(request, timbangan, technician),
            pw.SizedBox(height: 20),

            // Pre-adjustment Section
            _buildPreAdjustmentSection(request, allMetadata),
            pw.SizedBox(height: 15),

            // Linearity Section
            _buildLinearitySection(linearities),
            pw.SizedBox(height: 15),

            // Eccentricity Section
            _buildEccentricitySection(eccentricities),
            pw.SizedBox(height: 15),

            // Repeat Capacity Section
            _buildRepeatCapacitySection(repeatCapacities),
            pw.SizedBox(height: 15),

            // Hysteresis Section
            _buildHysteresisSection(histerisysList),
            pw.SizedBox(height: 20),

            // Environmental Conditions
            _buildEnvironmentalSection(request),
            pw.SizedBox(height: 20),

            // Repeat Capacity Summary
            _buildRepeatCapacitySection(repeatCapacities),
            pw.SizedBox(height: 15),

            // Linearity Summary
            _buildLinearitySection(linearities),
            pw.SizedBox(height: 15),

            // Eccentricity Summary
            _buildEccentricitySection(eccentricities),
            pw.SizedBox(height: 15),

            // Hysteresis Summary
            _buildHysteresisSection(histerisysList),
          ],
        );
      },
    );
  }

  pw.Widget _buildAlmegaHeader(
    CalibrationRequest request,
    Map<String, dynamic>? timbangan,
    Map<String, dynamic>? technician,
  ) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.black),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // ALMEGA Header Row
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'ALMEGA',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'LEMBAR KERJA KALIBRASI TIMBANGAN ELEKTRONIK',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'ALMEGA',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text('Order No. ${request.orderNumber}'),
                ],
              ),
            ],
          ),
          pw.SizedBox(height: 15),

          // Equipment Information
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Left Column
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow('Nama Alat', 'Timbangan Elektronik'),
                    _buildInfoRow('Kapasitas', timbangan?['capacity'] ?? 'N/A'),
                    _buildInfoRow('Merk', timbangan?['brand'] ?? ''),
                    _buildInfoRow('Type / Model', timbangan?['model'] ?? ''),
                    _buildInfoRow('Serial Number', timbangan?['snr'] ?? ''),
                    _buildInfoRow('Resolusi', timbangan?['resolution'] ?? ''),
                  ],
                ),
              ),
              pw.SizedBox(width: 20),
              // Right Column
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow('Sender', request.companyName),
                    _buildInfoRow(
                      'No. Inventaris',
                      timbangan?['id_timbangan'] ?? '',
                    ),
                    _buildInfoRow(
                      'Lokasi Kalibrasi',
                      timbangan?['location'] ?? '',
                    ),
                    _buildInfoRow(
                      'Acuan',
                      'OIML R76-1:2006 & OIML R111-1:2004',
                    ),
                    _buildInfoRow(
                      'Dikalibrasi Oleh',
                      technician?['tech_name'] ?? 'N/A',
                    ),
                    _buildInfoRow(
                      'Tanggal Kalibrasi',
                      request.dateCalibration?.toString().split(' ')[0] ??
                          'N/A',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 100,
            child: pw.Text(
              label,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Text(': '),
          pw.Expanded(child: pw.Text(value)),
        ],
      ),
    );
  }

  pw.Widget _buildPreAdjustmentSection(
    CalibrationRequest request,
    Map<String, dynamic> allMetadata,
  ) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            '1. Penyetelan Pra-Adjustment',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Table(
            border: pw.TableBorder.all(),
            children: [
              pw.TableRow(
                children: [
                  _buildTableCell('Massa Standar (kg)', isHeader: true),
                  _buildTableCell('Pembacaan (kg)', isHeader: true),
                  _buildTableCell('Nilai (kg)', isHeader: true),
                  _buildTableCell('Pembacaan (kg)', isHeader: true),
                ],
              ),
              pw.TableRow(
                children: [
                  _buildTableCell('${request.preadjustmentNominal ?? 0.0}'),
                  _buildTableCell('${request.preadjustmentValue1 ?? 0.0}'),
                  _buildTableCell('${request.preadjustmentValue2 ?? 0.0}'),
                  _buildTableCell('${request.preadjustmentValue3 ?? 0.0}'),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildHeader() {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'CALIBRATION REPORT',
            style: pw.TextStyle(
              fontSize: 20,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'eKalibrasi System',
            style: pw.TextStyle(fontSize: 12, color: PdfColors.blue600),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Generated on: ${DateTime.now().toString().substring(0, 19)}',
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildBasicInfoSection(CalibrationRequest request) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('BASIC INFORMATION'),
          pw.SizedBox(height: 10),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            columnWidths: {
              0: const pw.FlexColumnWidth(1),
              1: const pw.FlexColumnWidth(2),
            },
            children: [
              _buildTableRow('Order Number', request.orderNumber),
              _buildTableRow('Company Name', request.companyName),
              _buildTableRow('Company Address', request.companyAddress),
              _buildTableRow('Contact Person', request.contactPerson),
              _buildTableRow('Division', request.cpDivision ?? ''),
              _buildTableRow('Certificate Number', request.certNumber ?? ''),
              _buildTableRow(
                'Calibration Date',
                request.dateCalibration?.toString().substring(0, 10) ?? '',
              ),
              _buildTableRow('Status', request.status),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildEnvironmentalSection(CalibrationRequest request) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('ENVIRONMENTAL CONDITIONS'),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Before Calibration',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Table(
                      border: pw.TableBorder.all(color: PdfColors.grey300),
                      columnWidths: {
                        0: const pw.FlexColumnWidth(1),
                        1: const pw.FlexColumnWidth(1),
                      },
                      children: [
                        _buildTableRow(
                          'Temperature',
                          '${request.beforeTemp ?? ''} °C',
                        ),
                        _buildTableRow(
                          'Humidity',
                          '${request.beforeHumidity ?? ''} %',
                        ),
                        _buildTableRow(
                          'Barometer',
                          '${request.beforeBarrometer ?? ''} mbar',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'After Calibration',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Table(
                      border: pw.TableBorder.all(color: PdfColors.grey300),
                      columnWidths: {
                        0: const pw.FlexColumnWidth(1),
                        1: const pw.FlexColumnWidth(1),
                      },
                      children: [
                        _buildTableRow(
                          'Temperature',
                          '${request.afterTemp ?? ''} °C',
                        ),
                        _buildTableRow(
                          'Humidity',
                          '${request.afterHumidity ?? ''} %',
                        ),
                        _buildTableRow(
                          'Barometer',
                          '${request.afterBarrometer ?? ''} mbar',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildRepeatCapacitySection(
    List<CalibrationRepeatCapacity> repeatCapacities,
  ) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('REPEAT CAPACITY'),
          pw.SizedBox(height: 10),
          if (repeatCapacities.isEmpty)
            pw.Text('No repeat capacity data available')
          else
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(1),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FlexColumnWidth(3),
                4: const pw.FlexColumnWidth(1),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    _buildTableCell('Type', isHeader: true),
                    _buildTableCell('Range', isHeader: true),
                    _buildTableCell('Nominal', isHeader: true),
                    _buildTableCell('Values', isHeader: true),
                    _buildTableCell('Pass', isHeader: true),
                  ],
                ),
                ...repeatCapacities.take(5).map((capacity) {
                  String displayValue = _parseJsonValues(
                    capacity.capacityValue,
                  );
                  return pw.TableRow(
                    children: [
                      _buildTableCell(capacity.repeatType),
                      _buildTableCell(capacity.rangeType.toString()),
                      _buildTableCell(capacity.capacityNominal.toString()),
                      _buildTableCell(displayValue),
                      _buildTableCell(capacity.isPass ? 'Yes' : 'No'),
                    ],
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  pw.Widget _buildLinearitySection(List<CalibrationLinearity> linearities) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('LINEARITY'),
          pw.SizedBox(height: 10),
          if (linearities.isEmpty)
            pw.Text('No linearity data available')
          else
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FlexColumnWidth(1),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    _buildTableCell('Nominal', isHeader: true),
                    _buildTableCell('Conventional Values', isHeader: true),
                    _buildTableCell('Reading', isHeader: true),
                    _buildTableCell('Pass', isHeader: true),
                  ],
                ),
                ...linearities.take(5).map((linearity) {
                  String displayValue = _parseLinearityValues(
                    linearity.linearityValue,
                  );
                  return pw.TableRow(
                    children: [
                      _buildTableCell(linearity.linearityNominal.toString()),
                      _buildTableCell(displayValue),
                      _buildTableCell(linearity.reading1.toString()),
                      _buildTableCell(linearity.isPass ? 'Yes' : 'No'),
                    ],
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  pw.Widget _buildEccentricitySection(
    List<CalibrationEccentricity> eccentricities,
  ) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('ECCENTRICITY'),
          pw.SizedBox(height: 10),
          if (eccentricities.isEmpty)
            pw.Text('No eccentricity data available')
          else
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(3),
                2: const pw.FlexColumnWidth(1),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    _buildTableCell('Nominal', isHeader: true),
                    _buildTableCell('Position Values', isHeader: true),
                    _buildTableCell('Pass', isHeader: true),
                  ],
                ),
                ...eccentricities.take(3).map((eccentricity) {
                  String displayValue = _parseEccentricityValues(
                    eccentricity.eccentricityValue,
                  );
                  return pw.TableRow(
                    children: [
                      _buildTableCell(
                        eccentricity.eccentricityNominal.toString(),
                      ),
                      _buildTableCell(displayValue),
                      _buildTableCell(eccentricity.isPass ? 'Yes' : 'No'),
                    ],
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  pw.Widget _buildHysteresisSection(
    List<CalibrationHisterisys> histerisysList,
  ) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('HYSTERESIS'),
          pw.SizedBox(height: 10),
          if (histerisysList.isEmpty)
            pw.Text('No hysteresis data available')
          else
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(1),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(2),
                4: const pw.FlexColumnWidth(1),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                  children: [
                    _buildTableCell('M Nominal', isHeader: true),
                    _buildTableCell('M\' Nominal', isHeader: true),
                    _buildTableCell('M Values', isHeader: true),
                    _buildTableCell('M\' Values', isHeader: true),
                    _buildTableCell('Pass', isHeader: true),
                  ],
                ),
                ...histerisysList.take(3).map((histerisys) {
                  String mDisplayValue = _parseJsonValues(histerisys.mValue);
                  String m1DisplayValue = _parseJsonValues(histerisys.m1Value);
                  return pw.TableRow(
                    children: [
                      _buildTableCell(histerisys.mNominal.toString()),
                      _buildTableCell(histerisys.m1Nominal.toString()),
                      _buildTableCell(mDisplayValue),
                      _buildTableCell(m1DisplayValue),
                      _buildTableCell(histerisys.isPass ? 'Yes' : 'No'),
                    ],
                  );
                }),
              ],
            ),
        ],
      ),
    );
  }

  pw.Page _buildDataDetailsPage(
    List<CalibrationRepeatCapacity> repeatCapacities,
    List<CalibrationLinearity> linearities,
    List<CalibrationEccentricity> eccentricities,
    List<CalibrationHisterisys> histerisysList,
  ) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'DETAILED CALIBRATION DATA',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue800,
              ),
            ),
            pw.SizedBox(height: 20),

            // Complete Repeat Capacity Data
            if (repeatCapacities.length > 5) ...[
              _buildSectionTitle('COMPLETE REPEAT CAPACITY DATA'),
              pw.SizedBox(height: 10),
              _buildCompleteRepeatCapacityTable(repeatCapacities),
              pw.SizedBox(height: 20),
            ],

            // Complete Linearity Data
            if (linearities.length > 5) ...[
              _buildSectionTitle('COMPLETE LINEARITY DATA'),
              pw.SizedBox(height: 10),
              _buildCompleteLinearityTable(linearities),
            ],
          ],
        );
      },
    );
  }

  pw.Widget _buildCompleteRepeatCapacityTable(
    List<CalibrationRepeatCapacity> repeatCapacities,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(3),
        4: const pw.FlexColumnWidth(1),
      },
      children: [
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Type', isHeader: true),
            _buildTableCell('Range', isHeader: true),
            _buildTableCell('Nominal', isHeader: true),
            _buildTableCell('Values', isHeader: true),
            _buildTableCell('Pass', isHeader: true),
          ],
        ),
        ...repeatCapacities.map((capacity) {
          String displayValue = _parseJsonValues(capacity.capacityValue);
          return pw.TableRow(
            children: [
              _buildTableCell(capacity.repeatType),
              _buildTableCell(capacity.rangeType.toString()),
              _buildTableCell(capacity.capacityNominal.toString()),
              _buildTableCell(displayValue),
              _buildTableCell(capacity.isPass ? 'Yes' : 'No'),
            ],
          );
        }),
      ],
    );
  }

  pw.Widget _buildCompleteLinearityTable(
    List<CalibrationLinearity> linearities,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(1),
        3: const pw.FlexColumnWidth(1),
      },
      children: [
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Nominal', isHeader: true),
            _buildTableCell('Conventional Values', isHeader: true),
            _buildTableCell('Reading', isHeader: true),
            _buildTableCell('Pass', isHeader: true),
          ],
        ),
        ...linearities.map((linearity) {
          String displayValue = _parseLinearityValues(linearity.linearityValue);
          return pw.TableRow(
            children: [
              _buildTableCell(linearity.linearityNominal.toString()),
              _buildTableCell(displayValue),
              _buildTableCell(linearity.reading1.toString()),
              _buildTableCell(linearity.isPass ? 'Yes' : 'No'),
            ],
          );
        }),
      ],
    );
  }

  // Helper methods
  pw.Widget _buildSectionTitle(String title) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue100,
        border: pw.Border.all(color: PdfColors.blue300),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          fontSize: 12,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.blue800,
        ),
      ),
    );
  }

  pw.TableRow _buildTableRow(String label, String value) {
    return pw.TableRow(
      children: [
        _buildTableCell(label, isHeader: true),
        _buildTableCell(value),
      ],
    );
  }

  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(color: isHeader ? PdfColors.grey100 : null),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  String _parseJsonValues(String jsonString) {
    try {
      final jsonData = jsonDecode(jsonString);
      if (jsonData is Map<String, dynamic> && jsonData.containsKey('values')) {
        final values = jsonData['values'] as List;
        return values.take(5).join(', ') + (values.length > 5 ? '...' : '');
      }
    } catch (e) {
      // If not JSON, return truncated string
      final parts = jsonString.split(',');
      return parts.take(5).join(', ') + (parts.length > 5 ? '...' : '');
    }
    return jsonString.length > 50
        ? '${jsonString.substring(0, 50)}...'
        : jsonString;
  }

  String _parseLinearityValues(String jsonString) {
    try {
      final jsonData = jsonDecode(jsonString);
      if (jsonData is Map<String, dynamic> &&
          jsonData.containsKey('conventional_values')) {
        final values = jsonData['conventional_values'] as List;
        return values.take(5).join(', ') + (values.length > 5 ? '...' : '');
      }
    } catch (e) {
      // If not JSON, return truncated string
      final parts = jsonString.split(',');
      return parts.take(5).join(', ') + (parts.length > 5 ? '...' : '');
    }
    return jsonString.length > 50
        ? '${jsonString.substring(0, 50)}...'
        : jsonString;
  }

  String _parseEccentricityValues(String jsonString) {
    try {
      final jsonData = jsonDecode(jsonString);
      if (jsonData is Map<String, dynamic> && jsonData.containsKey('values')) {
        final values = jsonData['values'] as List;
        final positions =
            jsonData['positions'] as List? ?? ['C', 'F', 'B', 'R', 'L'];
        final displayParts = <String>[];
        for (
          int i = 0;
          i < values.length && i < positions.length && i < 5;
          i++
        ) {
          displayParts.add('${positions[i]}: ${values[i]}');
        }
        return displayParts.join(', ');
      }
    } catch (e) {
      // If not JSON, return truncated string
      return jsonString.length > 50
          ? '${jsonString.substring(0, 50)}...'
          : jsonString;
    }
    return jsonString.length > 50
        ? '${jsonString.substring(0, 50)}...'
        : jsonString;
  }

  // Helper method to safely parse double values from dynamic data
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }
}
