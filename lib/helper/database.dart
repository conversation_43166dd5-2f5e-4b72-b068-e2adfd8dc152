import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static const _databaseName = 'ekalibrasi.db';
  static const _databaseVersion = 4;

  // Singleton pattern
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();
  DatabaseHelper._privateConstructor();

  static Database? _database;
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    print('Database path: $databasesPath');
    final path = join(databasesPath, _databaseName);

    try {
      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: (db) async {
          print('Database opened successfully');
          // Enable foreign key constraints
          await db.execute('PRAGMA foreign_keys = ON');
        },
      );
    } catch (e) {
      print('Error opening database: $e');
      // If there's an error, try to delete and recreate the database
      await deleteDatabase(path);
      print('Database deleted, recreating...');
      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: (db) async {
          print('Database recreated successfully');
          await db.execute('PRAGMA foreign_keys = ON');
        },
      );
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    print('Creating database tables...');

    try {
      // Create all tables

      // Master branches table
      print('Creating master_branches table...');
      await db.execute('''
      CREATE TABLE master_branches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        branch_code TEXT UNIQUE,
        branch_name TEXT,
        active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT
      )
    ''');

      // Master technicians table
      print('Creating master_technicians table...');
      await db.execute('''
      CREATE TABLE master_technicians (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        tech_name TEXT,
        tech_initial TEXT,
        branch_id INTEGER,
        active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (branch_id) REFERENCES master_branches (id)
      )
    ''');

      // Master timbangan table
      print('Creating master_timbangan table...');
      await db.execute('''
      CREATE TABLE master_timbangan (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        id_timbangan TEXT UNIQUE,
        timbangan_type TEXT,
        brand TEXT,
        model TEXT,
        snr TEXT,
        capacity TEXT,
        location TEXT,
        branch_id INTEGER,
        active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (branch_id) REFERENCES master_branches (id)
      )
    ''');

      // Master anak timbang table
      print('Creating master_anak_timbang table...');
      await db.execute('''
      CREATE TABLE master_anak_timbang (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        no_invent TEXT NOT NULL,
        branch_id INTEGER NOT NULL,
        value TEXT NOT NULL,
        active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (branch_id) REFERENCES master_branches (id)
      )
    ''');

      // Master standard table
      print('Creating master_standard table...');
      await db.execute('''
      CREATE TABLE master_standard (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        no_invent TEXT NOT NULL,
        branch_id INTEGER NOT NULL,
        value TEXT NOT NULL,
        active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (branch_id) REFERENCES master_branches (id)
      )
    ''');

      // Calibration requests table
      print('Creating calibration_requests table...');
      await db.execute('''
      CREATE TABLE calibration_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        order_number TEXT,
        company_name TEXT,
        company_address TEXT,
        contact_person TEXT,
        cp_division TEXT,
        cert_number TEXT,
        date_calibration TEXT,
        technician_uuid TEXT,
        timbangan_uuid TEXT,
        daya_baca_r1_value REAL,
        daya_baca_r1_unit TEXT,
        daya_baca_r2_value REAL,
        daya_baca_r2_unit TEXT,
        daya_baca_r3_value REAL,
        daya_baca_r3_unit TEXT,
        accuration_percentage REAL,
        before_temp REAL,
        before_humidity REAL,
        before_barrometer REAL,
        after_temp REAL,
        after_humidity REAL,
        after_barrometer REAL,
        standard_uuid TEXT,
        anak_timbang_list TEXT,
        preadjustment_anak_timbang_uuid TEXT,
        preadjustment_nominal REAL,
        preadjustment_value_1 REAL,
        preadjustment_value_2 REAL,
        preadjustment_value_3 REAL,
        preadjustment_value_4 REAL,
        all_metadata TEXT,
        status TEXT DEFAULT 'cust-created',
        certificate_pic TEXT,
        synced INTEGER DEFAULT 0,
        sync_time TEXT,
        active INTEGER DEFAULT 1,
        branch_creator TEXT,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (branch_creator) REFERENCES master_branches (uuid),
        FOREIGN KEY (timbangan_uuid) REFERENCES master_timbangan (uuid),
        FOREIGN KEY (preadjustment_anak_timbang_uuid) REFERENCES master_anak_timbang (uuid),
        FOREIGN KEY (standard_uuid) REFERENCES master_standard (uuid)
      )
    ''');

      // Roles table
      print('Creating roles table...');
      await db.execute('''
      CREATE TABLE roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        rolename TEXT,
        authorization TEXT,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT
      )
    ''');

      // Users table
      print('Creating users table...');
      await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        technician_id INTEGER,
        username TEXT,
        password TEXT,
        role_id INTEGER,
        login_retry INTEGER DEFAULT 0,
        login_locked INTEGER DEFAULT 0,
        login_locked_at TEXT,
        last_login TEXT,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT
      )
    ''');

      // Create related calibration tables
      await _createRelatedCalibrationTables(db);

      // Create unique indexes
      await _createIndexes(db);

      // Insert default admin user
      await _insertDefaultUser(db);

      print('All database tables created successfully');
    } catch (e) {
      print('Error creating database tables: $e');
      rethrow;
    }
  }

  Future<void> _insertDefaultUser(Database db) async {
    // Insert default admin role first
    await db.insert('roles', {
      'uuid': 'admin-role-uuid',
      'rolename': 'admin',
      'authorization': '{"all": true}',
      'active': 1,
      'created_by': 'system',
      'created_at': DateTime.now().toIso8601String(),
    });

    // Insert default admin user (password: admin123)
    // In production, this should be changed immediately
    await db.insert('users', {
      'uuid': 'admin-user-uuid',
      'username': 'admin',
      'password': 'admin123', // In production, this should be properly hashed
      'role_id': 1, // Reference to the admin role
      'active': 1,
      'created_by': 'system',
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<void> _createRelatedCalibrationTables(Database db) async {
    print('Creating calibration_repeat_capacity table...');
    await db.execute('''
      CREATE TABLE calibration_repeat_capacity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        calibration_request_uuid TEXT,
        repeat_type TEXT,
        range_type INTEGER,
        capacity_nominal REAL,
        capacity_value TEXT,
        is_pass INTEGER DEFAULT 0,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (calibration_request_uuid) REFERENCES calibration_requests (uuid)
      )
    ''');

    print('Creating calibration_linearity table...');
    await db.execute('''
      CREATE TABLE calibration_linearity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        calibration_request_uuid TEXT,
        linearity_nominal REAL,
        linearity_value TEXT,
        reading_1 REAL,
        reading_2 REAL,
        reading_3 REAL,
        is_pass INTEGER DEFAULT 0,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (calibration_request_uuid) REFERENCES calibration_requests (uuid)
      )
    ''');

    print('Creating calibration_eccentricity table...');
    await db.execute('''
      CREATE TABLE calibration_eccentricity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        calibration_request_uuid TEXT,
        eccentricity_nominal REAL,
        eccentricity_value TEXT,
        is_pass INTEGER DEFAULT 0,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (calibration_request_uuid) REFERENCES calibration_requests (uuid)
      )
    ''');

    print('Creating calibration_histerisys table...');
    await db.execute('''
      CREATE TABLE calibration_histerisys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE,
        calibration_request_uuid TEXT,
        m_nominal REAL,
        m1_nominal REAL,
        m_value TEXT,
        m1_value TEXT,
        is_pass INTEGER DEFAULT 0,
        active INTEGER DEFAULT 1,
        created_by TEXT DEFAULT 'system',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT,
        FOREIGN KEY (calibration_request_uuid) REFERENCES calibration_requests (uuid)
      )
    ''');
  }

  Future<void> _createIndexes(Database db) async {
    print('Creating unique indexes...');

    // Create unique index for master_anak_timbang
    await db.execute('''
      CREATE UNIQUE INDEX master_anak_timbang_index_0
      ON master_anak_timbang (no_invent, branch_id)
    ''');

    // Create unique index for master_standard
    await db.execute('''
      CREATE UNIQUE INDEX master_standard_index_1
      ON master_standard (no_invent, branch_id)
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('Upgrading database from version $oldVersion to $newVersion');

    try {
      // Handle database upgrades here
      if (oldVersion < 2) {
        // Users table is already created in _onCreate, no need to create again
        print('Upgrading to version 2...');
        // Insert default admin user if not exists
        await _insertDefaultUser(db);
      }

      if (oldVersion < 3) {
        print('Upgrading to version 3...');
        // Add any new features for version 3
        // For now, just ensure all tables have proper primary keys
        await db.execute('PRAGMA foreign_keys = ON');
      }

      print('Database upgrade completed successfully');
    } catch (e) {
      print('Error during database upgrade: $e');
      rethrow;
    }
  }

  // Method to verify database schema
  Future<void> verifyDatabaseSchema() async {
    final db = await database;
    print('Verifying database schema...');

    try {
      // Check if tables exist and have proper primary keys
      final tables = [
        'users',
        'master_branches',
        'master_technicians',
        'master_timbangan',
        'master_anak_timbang',
        'master_standard',
        'calibration_requests',
        'calibration_repeat_capacity',
        'calibration_linearity',
        'calibration_eccentricity',
        'calibration_histerisys',
      ];

      for (final tableName in tables) {
        final result = await db.rawQuery("PRAGMA table_info($tableName)");
        if (result.isNotEmpty) {
          print('Table $tableName exists with ${result.length} columns');
          // Check if first column is id with primary key
          final firstColumn = result.first;
          if (firstColumn['name'] == 'id' && firstColumn['pk'] == 1) {
            print('✓ Table $tableName has proper primary key');
          } else {
            print('✗ Table $tableName missing proper primary key');
          }
        } else {
          print('✗ Table $tableName does not exist');
        }
      }

      print('Database schema verification completed');
    } catch (e) {
      print('Error verifying database schema: $e');
    }
  }

  // Method to force database recreation (for debugging)
  Future<void> recreateDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    print('Recreating database...');
    await deleteDatabase(path);
    _database = null; // Reset the singleton

    // Reinitialize database
    await database;
    print('Database recreated successfully');
  }
}
