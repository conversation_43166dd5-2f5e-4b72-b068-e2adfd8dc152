import 'dart:convert';
import 'package:ekalibrasi/helper/extension.dart';
import 'package:ekalibrasi/helper/model.dart';
import 'package:ekalibrasi/repositories/CalibrationRepository.dart';
import 'package:ekalibrasi/services/ExcelService.dart';
import 'package:ekalibrasi/services/CalibrationSyncService.dart';
import 'package:flutter/material.dart';

import 'package:shadcn_ui/shadcn_ui.dart';

class CalibrationRequestDetail extends StatefulWidget {
  final CalibrationRequest request;
  final CalibrationRepository repository;

  const CalibrationRequestDetail({
    super.key,
    required this.request,
    required this.repository,
  });

  @override
  State<CalibrationRequestDetail> createState() =>
      _CalibrationRequestDetailState();
}

class _CalibrationRequestDetailState extends State<CalibrationRequestDetail>
    with SingleTickerProviderStateMixin {
  late CalibrationRequest _request;
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  bool _isSaving = false;
  bool _isSyncing = false;

  // Controllers for text fields
  final _orderNumberController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _contactPersonController = TextEditingController();
  final _cpDivisionController = TextEditingController();
  final _certNumberController = TextEditingController();
  final _dayaBacaR1ValueController = TextEditingController();
  final _dayaBacaR1UnitController = TextEditingController();
  final _dayaBacaR2ValueController = TextEditingController();
  final _dayaBacaR2UnitController = TextEditingController();
  final _accurationPercentageController = TextEditingController();
  final _beforeTempController = TextEditingController();
  final _beforeHumidityController = TextEditingController();
  final _beforeBarrometerController = TextEditingController();
  final _afterTempController = TextEditingController();
  final _afterHumidityController = TextEditingController();
  final _afterBarrometerController = TextEditingController();
  final _preadjustmentNominalController = TextEditingController();
  final _preadjustmentValue1Controller = TextEditingController();
  final _preadjustmentValue2Controller = TextEditingController();
  final _preadjustmentValue3Controller = TextEditingController();
  final _preadjustmentValue4Controller = TextEditingController();

  List<CalibrationRepeatCapacity> _repeatCapacities = [];
  List<CalibrationLinearity> _linearities = [];
  List<CalibrationEccentricity> _eccentricities = [];
  List<CalibrationHisterisys> _histerisys = [];
  bool _isLoadingRelatedData = false;

  @override
  void initState() {
    super.initState();
    _request = widget.request;
    _initializeControllers();
    _tabController = TabController(length: 5, vsync: this);
    _loadRelatedData();
  }

  void _initializeControllers() {
    _orderNumberController.text = _request.orderNumber;
    _companyNameController.text = _request.companyName;
    _companyAddressController.text = _request.companyAddress;
    _contactPersonController.text = _request.contactPerson;
    _cpDivisionController.text = _request.cpDivision ?? '';
    _certNumberController.text = _request.certNumber ?? '';
    _dayaBacaR1ValueController.text =
        _request.dayaBacaR1Value?.toString() ?? '';
    _dayaBacaR1UnitController.text = _request.dayaBacaR1Unit ?? '';
    _dayaBacaR2ValueController.text =
        _request.dayaBacaR2Value?.toString() ?? '';
    _dayaBacaR2UnitController.text = _request.dayaBacaR2Unit ?? '';
    _accurationPercentageController.text =
        _request.accurationPercentage?.toString() ?? '';
    _beforeTempController.text = _request.beforeTemp?.toString() ?? '';
    _beforeHumidityController.text = _request.beforeHumidity?.toString() ?? '';
    _beforeBarrometerController.text =
        _request.beforeBarrometer?.toString() ?? '';
    _afterTempController.text = _request.afterTemp?.toString() ?? '';
    _afterHumidityController.text = _request.afterHumidity?.toString() ?? '';
    _afterBarrometerController.text =
        _request.afterBarrometer?.toString() ?? '';
    _preadjustmentNominalController.text =
        _request.preadjustmentNominal?.toString() ?? '';
    _preadjustmentValue1Controller.text =
        _request.preadjustmentValue1?.toString() ?? '';
    _preadjustmentValue2Controller.text =
        _request.preadjustmentValue2?.toString() ?? '';
    _preadjustmentValue3Controller.text =
        _request.preadjustmentValue3?.toString() ?? '';
    _preadjustmentValue4Controller.text =
        _request.preadjustmentValue4?.toString() ?? '';
  }

  @override
  void dispose() {
    _orderNumberController.dispose();
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _contactPersonController.dispose();
    _cpDivisionController.dispose();
    _certNumberController.dispose();
    _dayaBacaR1ValueController.dispose();
    _dayaBacaR1UnitController.dispose();
    _dayaBacaR2ValueController.dispose();
    _dayaBacaR2UnitController.dispose();
    _accurationPercentageController.dispose();
    _beforeTempController.dispose();
    _beforeHumidityController.dispose();
    _beforeBarrometerController.dispose();
    _afterTempController.dispose();
    _afterHumidityController.dispose();
    _afterBarrometerController.dispose();
    _preadjustmentNominalController.dispose();
    _preadjustmentValue1Controller.dispose();
    _preadjustmentValue2Controller.dispose();
    _preadjustmentValue3Controller.dispose();
    _preadjustmentValue4Controller.dispose();

    _tabController.dispose();
    super.dispose();
  }

  // Update the build method to include tabs
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_request.orderNumber),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Basic Info'),
            Tab(text: 'Repeat Capacity'),
            Tab(text: 'Linearity'),
            Tab(text: 'Eccentricity'),
            Tab(text: 'Hysteresis'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.table_chart),
            onPressed: _generateExcelReport,
            tooltip: 'Generate Excel Report',
          ),
          if (!_request.synced)
            IconButton(
              icon: _isSyncing
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Icon(Icons.sync),
              onPressed: _syncRequest,
              tooltip: 'Sync with server',
            ),
          IconButton(
            icon: _isSaving
                ? const CircularProgressIndicator(color: Colors.white)
                : const Icon(Icons.save),
            onPressed: _saveForm,
            tooltip: 'Save changes',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Basic Info Tab
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  _buildBasicInfoSection(),
                  const SizedBox(height: 20),
                  _buildCalibrationInfoSection(),
                  const SizedBox(height: 20),
                  _buildEnvironmentInfoSection(),
                  const SizedBox(height: 20),
                  _buildPreadjustmentSection(),
                  const SizedBox(height: 20),
                  _buildStatusSection(),
                ],
              ),
            ),
          ),

          // Repeat Capacity Tab
          _buildRepeatCapacityTab(),

          // Linearity Tab
          _buildLinearityTab(),

          // Eccentricity Tab
          _buildEccentricityTab(),

          // Hysteresis Tab
          _buildHysteresisTab(),
        ],
      ),
    );
  }

  Future<void> _loadRelatedData() async {
    setState(() => _isLoadingRelatedData = true);
    try {
      final capacities = await widget.repository.getRepeatCapacitiesForRequest(
        _request.uuid,
      );
      final linearities = await widget.repository.getLinearitiesForRequest(
        _request.uuid,
      );
      final eccentricities = await widget.repository
          .getEccentricitiesForRequest(_request.uuid);
      final histerisys = await widget.repository.getHisterisysForRequest(
        _request.uuid,
      );

      setState(() {
        _repeatCapacities = capacities;
        _linearities = linearities;
        _eccentricities = eccentricities;
        _histerisys = histerisys;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load related data: $e')),
      );
    } finally {
      setState(() => _isLoadingRelatedData = false);
    }
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            TextFormField(
              controller: _orderNumberController,
              decoration: const InputDecoration(labelText: 'Order Number'),
              onChanged: (value) =>
                  _request = _request.copyWith(orderNumber: value),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            TextFormField(
              controller: _companyNameController,
              decoration: const InputDecoration(labelText: 'Company Name'),
              onChanged: (value) =>
                  _request = _request.copyWith(companyName: value),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            TextFormField(
              controller: _companyAddressController,
              decoration: const InputDecoration(labelText: 'Company Address'),
              maxLines: 3,
              onChanged: (value) =>
                  _request = _request.copyWith(companyAddress: value),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            TextFormField(
              controller: _contactPersonController,
              decoration: const InputDecoration(labelText: 'Contact Person'),
              onChanged: (value) =>
                  _request = _request.copyWith(contactPerson: value),
              validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
            ),
            TextFormField(
              controller: _cpDivisionController,
              decoration: const InputDecoration(labelText: 'Division'),
              onChanged: (value) =>
                  _request = _request.copyWith(cpDivision: value),
            ),
            TextFormField(
              controller: _certNumberController,
              decoration: const InputDecoration(
                labelText: 'Certificate Number',
              ),
              onChanged: (value) =>
                  _request = _request.copyWith(certNumber: value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalibrationInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Calibration Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            // TODO: Implement dropdowns for technician, timbangan, standard
            TextFormField(
              controller: _dayaBacaR1ValueController,
              decoration: const InputDecoration(
                labelText: 'Daya Baca R1 Value',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                dayaBacaR1Value: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _dayaBacaR1UnitController,
              decoration: const InputDecoration(labelText: 'Daya Baca R1 Unit'),
              onChanged: (value) =>
                  _request = _request.copyWith(dayaBacaR1Unit: value),
            ),
            TextFormField(
              controller: _dayaBacaR2ValueController,
              decoration: const InputDecoration(
                labelText: 'Daya Baca R2 Value',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                dayaBacaR2Value: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _dayaBacaR2UnitController,
              decoration: const InputDecoration(labelText: 'Daya Baca R2 Unit'),
              onChanged: (value) =>
                  _request = _request.copyWith(dayaBacaR2Unit: value),
            ),
            TextFormField(
              controller: _accurationPercentageController,
              decoration: const InputDecoration(
                labelText: 'Accuration Percentage',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                accurationPercentage: double.tryParse(value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnvironmentInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Environment Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const Text(
              'Before Calibration',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            TextFormField(
              controller: _beforeTempController,
              decoration: const InputDecoration(labelText: 'Temperature (°C)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                beforeTemp: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _beforeHumidityController,
              decoration: const InputDecoration(labelText: 'Humidity (%)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                beforeHumidity: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _beforeBarrometerController,
              decoration: const InputDecoration(labelText: 'Barrometer (hPa)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                beforeBarrometer: double.tryParse(value),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'After Calibration',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            TextFormField(
              controller: _afterTempController,
              decoration: const InputDecoration(labelText: 'Temperature (°C)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                afterTemp: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _afterHumidityController,
              decoration: const InputDecoration(labelText: 'Humidity (%)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                afterHumidity: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _afterBarrometerController,
              decoration: const InputDecoration(labelText: 'Barrometer (hPa)'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                afterBarrometer: double.tryParse(value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreadjustmentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Preadjustment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            // TODO: Add dropdown for preadjustment anak timbang
            TextFormField(
              controller: _preadjustmentNominalController,
              decoration: const InputDecoration(labelText: 'Nominal'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                preadjustmentNominal: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _preadjustmentValue1Controller,
              decoration: const InputDecoration(labelText: 'Value 1'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                preadjustmentValue1: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _preadjustmentValue2Controller,
              decoration: const InputDecoration(labelText: 'Value 2'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                preadjustmentValue2: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _preadjustmentValue3Controller,
              decoration: const InputDecoration(labelText: 'Value 3'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                preadjustmentValue3: double.tryParse(value),
              ),
            ),
            TextFormField(
              controller: _preadjustmentValue4Controller,
              decoration: const InputDecoration(labelText: 'Value 4'),
              keyboardType: TextInputType.number,
              onChanged: (value) => _request = _request.copyWith(
                preadjustmentValue4: double.tryParse(value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            DropdownButtonFormField<String>(
              value: _request.status,
              items: const [
                DropdownMenuItem(
                  value: 'cust-created',
                  child: Text('Customer Created'),
                ),
                DropdownMenuItem(
                  value: 'in-progress',
                  child: Text('In Progress'),
                ),
                DropdownMenuItem(value: 'completed', child: Text('Completed')),
                DropdownMenuItem(value: 'canceled', child: Text('Canceled')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _request = _request.copyWith(status: value);
                  });
                }
              },
              decoration: const InputDecoration(labelText: 'Status'),
            ),
            const SizedBox(height: 16),
            Text(
              'Created at: ${DateFormat('dd MMM yyyy HH:mm').format(_request.createdAt)}',
            ),
            if (_request.updatedAt != null)
              Text(
                'Last updated: ${DateFormat('dd MMM yyyy HH:mm').format(_request.updatedAt!)}',
              ),
            if (!_request.synced)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'This request has not been synced with the server',
                  style: TextStyle(color: Colors.orange[700]),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() => _isSaving = true);

      try {
        // Update the request with current values
        final updatedRequest = _request.copyWith(updatedAt: DateTime.now());

        // Save to local database
        await widget.repository.updateCalibrationRequestLocal(updatedRequest);

        // Return the updated request to the previous screen
        if (mounted) {
          Navigator.pop(context, updatedRequest);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Failed to save request: $e')));
        }
      } finally {
        if (mounted) {
          setState(() => _isSaving = false);
        }
      }
    }
  }

  Widget _buildRepeatCapacityTab() {
    if (_isLoadingRelatedData) {
      return const Center(child: CircularProgressIndicator());
    }

    // Separate half and full capacity data
    final halfCapacities = _repeatCapacities
        .where((c) => c.repeatType == 'half')
        .toList();
    final fullCapacities = _repeatCapacities
        .where((c) => c.repeatType == 'full')
        .toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Half Capacity Section
          ShadCard(
            title: const Text('REPEAT Half Capacity'),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                _buildRepeatCapacitySection('Half Capacity', halfCapacities),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Full Capacity Section
          ShadCard(
            title: const Text('REPEAT Full Capacity'),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                _buildRepeatCapacitySection('Full Capacity', fullCapacities),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepeatCapacitySection(
    String title,
    List<CalibrationRepeatCapacity> capacities,
  ) {
    if (capacities.isEmpty) {
      return Text('No $title data available');
    }

    // Group by range type to show both value sets
    final range1 = capacities.where((c) => c.rangeType == 1).toList();
    final range2 = capacities.where((c) => c.rangeType == 2).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Show nominal value
        if (capacities.isNotEmpty) ...[
          Text('Nominal: ${capacities.first.capacityNominal}'),
          const SizedBox(height: 16),
        ],

        // Values Set 1
        if (range1.isNotEmpty) ...[
          const Text(
            'Values Set 1:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildCapacityValues(range1.first),
          const SizedBox(height: 16),
        ],

        // Values Set 2
        if (range2.isNotEmpty) ...[
          const Text(
            'Values Set 2:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildCapacityValues(range2.first),
        ],
      ],
    );
  }

  Widget _buildCapacityValues(CalibrationRepeatCapacity capacity) {
    // Parse JSON values
    List<String> values = [];
    try {
      final jsonData = jsonDecode(capacity.capacityValue);
      if (jsonData is Map<String, dynamic> && jsonData.containsKey('values')) {
        values = List<String>.from(jsonData['values']);
      }
    } catch (e) {
      // If not JSON, split by comma
      values = capacity.capacityValue.split(',');
    }

    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: values.asMap().entries.map((entry) {
        final index = entry.key;
        final value = entry.value;
        return SizedBox(
          width: 80,
          child: ShadInput(
            initialValue: value,
            readOnly: true,
            placeholder: Text('${index + 1}'),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLinearityTab() {
    if (_isLoadingRelatedData) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: ShadCard(
        title: const Text('Linearity'),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),

            // Unit display
            if (_linearities.isNotEmpty) ...[
              _buildLinearityUnitSection(),
              const SizedBox(height: 20),
            ],

            // Linearity data table
            _buildLinearityDataTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildLinearityUnitSection() {
    String unit = '';
    if (_linearities.isNotEmpty) {
      try {
        final jsonData = jsonDecode(_linearities.first.linearityValue);
        if (jsonData is Map<String, dynamic> && jsonData.containsKey('unit')) {
          unit = jsonData['unit'] ?? '';
        }
      } catch (e) {
        // If not JSON, use default
        unit = 'g';
      }
    }

    return Row(
      children: [
        const Text('Unit: ', style: TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(
          width: 100,
          child: ShadInput(
            initialValue: unit,
            readOnly: true,
            placeholder: const Text('Unit'),
          ),
        ),
      ],
    );
  }

  Widget _buildLinearityDataTable() {
    if (_linearities.isEmpty) {
      return const Text('No linearity data available');
    }

    return Column(
      children: [
        // Header row
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  'Nominal',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'Conventional Values',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'Reading',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Data rows
        ..._linearities.map((linearity) => _buildLinearityRow(linearity)),
      ],
    );
  }

  Widget _buildLinearityRow(CalibrationLinearity linearity) {
    // Parse JSON values
    List<String> conventionalValues = [];
    String reading = linearity.reading1.toString();

    try {
      final jsonData = jsonDecode(linearity.linearityValue);
      if (jsonData is Map<String, dynamic>) {
        if (jsonData.containsKey('conventional_values')) {
          conventionalValues = List<String>.from(
            jsonData['conventional_values'],
          );
        }
        if (jsonData.containsKey('reading')) {
          reading = jsonData['reading'] ?? linearity.reading1.toString();
        }
      }
    } catch (e) {
      // If not JSON, split by comma
      conventionalValues = linearity.linearityValue.split(',');
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          // Nominal
          Expanded(
            flex: 2,
            child: ShadInput(
              initialValue: linearity.linearityNominal.toString(),
              readOnly: true,
            ),
          ),

          const SizedBox(width: 8),

          // Conventional Values
          Expanded(
            flex: 4,
            child: Wrap(
              spacing: 4.0,
              children: conventionalValues.asMap().entries.map((entry) {
                return SizedBox(
                  width: 60,
                  child: ShadInput(
                    initialValue: entry.value,
                    readOnly: true,
                    placeholder: Text('${entry.key + 1}'),
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(width: 8),

          // Reading
          Expanded(
            flex: 2,
            child: ShadInput(initialValue: reading, readOnly: true),
          ),
        ],
      ),
    );
  }

  Widget _buildEccentricityTab() {
    if (_isLoadingRelatedData) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: ShadCard(
        title: const Text('Eccentricity'),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [const SizedBox(height: 16), _buildEccentricitySection()],
        ),
      ),
    );
  }

  Widget _buildEccentricitySection() {
    if (_eccentricities.isEmpty) {
      return const Text('No eccentricity data available');
    }

    final eccentricity = _eccentricities.first;

    // Parse JSON values
    List<String> values = [];
    List<String> positions = ['C', 'F', 'B', 'R', 'L'];

    try {
      final jsonData = jsonDecode(eccentricity.eccentricityValue);
      if (jsonData is Map<String, dynamic>) {
        if (jsonData.containsKey('values')) {
          values = List<String>.from(jsonData['values']);
        }
        if (jsonData.containsKey('positions')) {
          positions = List<String>.from(jsonData['positions']);
        }
      }
    } catch (e) {
      // If not JSON, split by comma
      values = eccentricity.eccentricityValue.split(',');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Nominal
        Row(
          children: [
            const Text(
              'Nominal: ',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(
              width: 100,
              child: ShadInput(
                initialValue: eccentricity.eccentricityNominal.toString(),
                readOnly: true,
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Position values
        const Text(
          'Position Values:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            childAspectRatio: 1.0,
            crossAxisSpacing: 8.0,
            mainAxisSpacing: 8.0,
          ),
          itemCount: positions.length,
          itemBuilder: (context, index) {
            return Column(
              children: [
                Container(
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(
                    child: Text(
                      positions[index],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                ShadInput(
                  initialValue: index < values.length ? values[index] : '',
                  readOnly: true,
                  placeholder: const Text('value'),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildHysteresisTab() {
    if (_isLoadingRelatedData) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: ShadCard(
        title: const Text('Hysteresis'),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [const SizedBox(height: 16), _buildHysteresisSection()],
        ),
      ),
    );
  }

  Widget _buildHysteresisSection() {
    if (_histerisys.isEmpty) {
      return const Text('No hysteresis data available');
    }

    final histerisys = _histerisys.first;

    // Parse JSON values
    List<String> mValues = [];
    List<String> m1Values = [];

    try {
      final mJsonData = jsonDecode(histerisys.mValue);
      if (mJsonData is Map<String, dynamic> &&
          mJsonData.containsKey('values')) {
        mValues = List<String>.from(mJsonData['values']);
      }
    } catch (e) {
      mValues = histerisys.mValue.split(',');
    }

    try {
      final m1JsonData = jsonDecode(histerisys.m1Value);
      if (m1JsonData is Map<String, dynamic> &&
          m1JsonData.containsKey('values')) {
        m1Values = List<String>.from(m1JsonData['values']);
      }
    } catch (e) {
      m1Values = histerisys.m1Value.split(',');
    }

    final histerisysLabels = [
      'M(p1)',
      'M+M\'',
      'M(p2)',
      'M+M\'',
      'M(p3)',
      'M+M\'',
      'M(p4)',
      'M+M\'',
      'M(p1)',
      'M+M\'',
      'M(p2)',
      'M+M\'',
      'M(p3)',
      'M+M\'',
      'M(p4)',
      'M+M\'',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Nominal values
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'M:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ShadInput(
                    initialValue: histerisys.mNominal.toString(),
                    readOnly: true,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'M\':',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ShadInput(
                    initialValue: histerisys.m1Nominal.toString(),
                    readOnly: true,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Values grid
        const Text('Values:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),

        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            childAspectRatio: 0.8,
            crossAxisSpacing: 4.0,
            mainAxisSpacing: 8.0,
          ),
          itemCount: 16,
          itemBuilder: (context, index) {
            final isSecondRow = index >= 8;
            final values = isSecondRow ? m1Values : mValues;
            final valueIndex = isSecondRow ? index - 8 : index;

            return Column(
              children: [
                Container(
                  height: 25,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(
                    child: Text(
                      histerisysLabels[index],
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                ShadInput(
                  initialValue: valueIndex < values.length
                      ? values[valueIndex]
                      : '',
                  readOnly: true,
                  placeholder: const Text('value'),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Future<void> _syncRequest() async {
    setState(() => _isSyncing = true);

    try {
      // First save any local changes to main request
      if (_formKey.currentState?.validate() ?? false) {
        final updatedRequest = _request.copyWith(updatedAt: DateTime.now());
        await widget.repository.updateCalibrationRequestLocal(updatedRequest);
        _request = updatedRequest;
      }

      // Use new CalibrationSyncService
      final syncService = CalibrationSyncService(
        dbHelper: widget.repository.dbHelper,
        apiBaseUrl: widget.repository.apiBaseUrl,
      );

      final result = await syncService.syncCalibrationRequests();

      if (result.success) {
        // Refresh all data
        final syncedRequests = await widget.repository
            .getCalibrationRequestsLocal();
        final syncedRequest = syncedRequests.firstWhere(
          (r) => r.uuid == _request.uuid,
          orElse: () => _request,
        );

        setState(() {
          _request = syncedRequest;
          _initializeControllers();
        });

        await _loadRelatedData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Sync completed successfully\nUploaded: ${result.uploadedCount}, Downloaded: ${result.downloadedCount}',
              ),
            ),
          );
        }
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Sync failed: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSyncing = false);
      }
    }
  }

  Future<void> _generateExcelReport() async {
    if (_request.id == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot generate report: Request ID is null'),
        ),
      );
      return;
    }

    try {
      final excelService = ExcelService(repository: widget.repository);
      final filePath = await excelService.generateCalibrationExcel(
        _request.uuid!,
      );

      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Reports Generated'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Excel and PDF reports have been generated successfully!',
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Excel file location:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  SelectableText(
                    filePath,
                    style: const TextStyle(
                      fontSize: 11,
                      fontFamily: 'monospace',
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'PDF file location:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  SelectableText(
                    filePath
                        .replaceAll('excel_reports', 'pdf_reports')
                        .replaceAll('.xlsx', '.pdf'),
                    style: const TextStyle(
                      fontSize: 11,
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to generate Excel report: $e')),
        );
      }
    }
  }
}
