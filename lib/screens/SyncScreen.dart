import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../services/MasterDataSyncService.dart';
import '../services/CalibrationSyncService.dart';
import '../repositories/CalibrationRepository.dart';

class SyncScreen extends StatefulWidget {
  final CalibrationRepository repository;
  final VoidCallback onSyncComplete;
  final VoidCallback? onUnauthorized;

  const SyncScreen({
    super.key,
    required this.repository,
    required this.onSyncComplete,
    this.onUnauthorized,
  });

  @override
  State<SyncScreen> createState() => _SyncScreenState();
}

class _SyncScreenState extends State<SyncScreen> {
  late MasterDataSyncService _syncService;
  late CalibrationSyncService _calibrationSyncService;
  bool _isSyncing = false;
  String _currentStep = '';
  double _progress = 0.0;
  String _statusMessage = 'Preparing to sync...';
  bool _hasError = false;

  final List<String> _syncSteps = [
    'Syncing branches...',
    'Syncing timbangan...',
    'Syncing technicians...',
    'Syncing anak timbang...',
    'Syncing standards...',
    'Syncing calibration requests...',
  ];

  @override
  void initState() {
    super.initState();
    _syncService = MasterDataSyncService(
      dbHelper: widget.repository.dbHelper,
      apiBaseUrl: widget.repository.apiBaseUrl,
      onUnauthorized: widget.onUnauthorized,
    );
    _calibrationSyncService = CalibrationSyncService(
      dbHelper: widget.repository.dbHelper,
      apiBaseUrl: widget.repository.apiBaseUrl,
      onUnauthorized: widget.onUnauthorized,
    );
    _startSync();
  }

  Future<void> _startSync() async {
    setState(() {
      _isSyncing = true;
      _hasError = false;
      _statusMessage = 'Starting synchronization...';
    });

    try {
      // Check if sync is needed
      if (!await _syncService.shouldSync()) {
        setState(() {
          _statusMessage = 'No internet connection or authentication required';
          _hasError = true;
          _isSyncing = false;
        });
        return;
      }

      // Simulate step-by-step sync with progress updates
      for (int i = 0; i < _syncSteps.length; i++) {
        setState(() {
          _currentStep = _syncSteps[i];
          _progress = (i + 1) / _syncSteps.length;
          _statusMessage = _syncSteps[i];
        });

        // Add a small delay to show progress
        await Future.delayed(const Duration(milliseconds: 500));

        // Perform the actual sync step
        switch (i) {
          case 0:
            await _syncService.syncBranches();
            break;
          case 1:
            await _syncService.syncTimbangan();
            break;
          case 2:
            await _syncService.syncTechnicians();
            break;
          case 3:
            await _syncService.syncAnakTimbang();
            break;
          case 4:
            await _syncService.syncStandards();
            break;
          case 5:
            await _calibrationSyncService.syncCalibrationRequests();
            break;
        }
      }

      setState(() {
        _statusMessage = 'Synchronization completed successfully!';
        _isSyncing = false;
      });

      // Wait a moment to show success message
      await Future.delayed(const Duration(seconds: 1));

      // Call completion callback
      widget.onSyncComplete();
    } catch (e) {
      setState(() {
        _statusMessage = 'Sync failed: ${e.toString()}';
        _hasError = true;
        _isSyncing = false;
      });
    }
  }

  void _retrySync() {
    _startSync();
  }

  void _skipSync() {
    widget.onSyncComplete();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Image.asset(
                'assets/images/logo-almega.png',
                height: 60,
                width: 150,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 32),

              // Title
              Text(
                'Synchronizing Data',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.foreground,
                ),
              ),
              const SizedBox(height: 8),

              // Subtitle
              Text(
                'Please wait while we sync the latest data',
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),

              // Progress indicator
              if (_isSyncing) ...[
                SizedBox(
                  width: double.infinity,
                  child: LinearProgressIndicator(
                    value: _progress,
                    backgroundColor: theme.colorScheme.muted,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Progress percentage
                Text(
                  '${(_progress * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Status message
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _hasError
                      ? theme.colorScheme.destructive.withOpacity(0.1)
                      : theme.colorScheme.muted,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _hasError
                        ? theme.colorScheme.destructive
                        : theme.colorScheme.border,
                  ),
                ),
                child: Row(
                  children: [
                    if (_isSyncing)
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary,
                          ),
                        ),
                      )
                    else if (_hasError)
                      Icon(
                        Icons.error_outline,
                        color: theme.colorScheme.destructive,
                        size: 20,
                      )
                    else
                      Icon(
                        Icons.check_circle_outline,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _statusMessage,
                        style: TextStyle(
                          color: _hasError
                              ? theme.colorScheme.destructive
                              : theme.colorScheme.foreground,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Action buttons
              if (_hasError) ...[
                Row(
                  children: [
                    Expanded(
                      child: ShadButton.outline(
                        onPressed: _skipSync,
                        child: const Text('Skip Sync'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ShadButton(
                        onPressed: _retrySync,
                        child: const Text('Retry'),
                      ),
                    ),
                  ],
                ),
              ] else if (!_isSyncing) ...[
                ShadButton(
                  onPressed: widget.onSyncComplete,
                  child: const Text('Continue'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
