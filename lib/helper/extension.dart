import 'package:ekalibrasi/helper/model.dart';

extension CalibrationRequestCopyWith on CalibrationRequest {
  CalibrationRequest copyWith({
    int? id,
    String? uuid,
    String? orderNumber,
    String? companyName,
    String? companyAddress,
    String? contactPerson,
    String? cpDivision,
    String? certNumber,
    DateTime? dateCalibration,
    String? technicianUuid,
    String? timbanganUuid,
    double? dayaBacaR1Value,
    String? dayaBacaR1Unit,
    double? dayaBacaR2Value,
    String? dayaBacaR2Unit,
    double? accurationPercentage,
    double? beforeTemp,
    double? beforeHumidity,
    double? beforeBarrometer,
    double? afterTemp,
    double? afterHumidity,
    double? afterBarrometer,
    String? standardUuid,
    String? anakTimbangList,
    String? preadjustmentAnakTimbangUuid,
    double? preadjustmentNominal,
    double? preadjustmentValue1,
    double? preadjustmentValue2,
    double? preadjustmentValue3,
    double? preadjustmentValue4,
    String? status,
    String? certificatePic,
    bool? synced,
    DateTime? syncTime,
    bool? active,
    String? branchCreator,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CalibrationRequest(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      orderNumber: orderNumber ?? this.orderNumber,
      companyName: companyName ?? this.companyName,
      companyAddress: companyAddress ?? this.companyAddress,
      contactPerson: contactPerson ?? this.contactPerson,
      cpDivision: cpDivision ?? this.cpDivision,
      certNumber: certNumber ?? this.certNumber,
      dateCalibration: dateCalibration ?? this.dateCalibration,
      technicianUuid: technicianUuid ?? this.technicianUuid,
      timbanganUuid: timbanganUuid ?? this.timbanganUuid,
      dayaBacaR1Value: dayaBacaR1Value ?? this.dayaBacaR1Value,
      dayaBacaR1Unit: dayaBacaR1Unit ?? this.dayaBacaR1Unit,
      dayaBacaR2Value: dayaBacaR2Value ?? this.dayaBacaR2Value,
      dayaBacaR2Unit: dayaBacaR2Unit ?? this.dayaBacaR2Unit,
      accurationPercentage: accurationPercentage ?? this.accurationPercentage,
      beforeTemp: beforeTemp ?? this.beforeTemp,
      beforeHumidity: beforeHumidity ?? this.beforeHumidity,
      beforeBarrometer: beforeBarrometer ?? this.beforeBarrometer,
      afterTemp: afterTemp ?? this.afterTemp,
      afterHumidity: afterHumidity ?? this.afterHumidity,
      afterBarrometer: afterBarrometer ?? this.afterBarrometer,
      standardUuid: standardUuid ?? this.standardUuid,
      anakTimbangList: anakTimbangList ?? this.anakTimbangList,
      preadjustmentAnakTimbangUuid:
          preadjustmentAnakTimbangUuid ?? this.preadjustmentAnakTimbangUuid,
      preadjustmentNominal: preadjustmentNominal ?? this.preadjustmentNominal,
      preadjustmentValue1: preadjustmentValue1 ?? this.preadjustmentValue1,
      preadjustmentValue2: preadjustmentValue2 ?? this.preadjustmentValue2,
      preadjustmentValue3: preadjustmentValue3 ?? this.preadjustmentValue3,
      preadjustmentValue4: preadjustmentValue4 ?? this.preadjustmentValue4,
      status: status ?? this.status,
      certificatePic: certificatePic ?? this.certificatePic,
      synced: synced ?? this.synced,
      syncTime: syncTime ?? this.syncTime,
      active: active ?? this.active,
      branchCreator: branchCreator ?? this.branchCreator,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
