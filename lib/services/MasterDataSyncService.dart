import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../helper/database.dart';
import 'AuthService.dart';

// Custom exception for unauthorized access
class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);

  @override
  String toString() => 'UnauthorizedException: $message';
}

class MasterDataSyncService {
  final DatabaseHelper dbHelper;
  final String apiBaseUrl;
  final Function()? onUnauthorized; // Callback for 403/401 responses

  MasterDataSyncService({
    required this.dbHelper,
    required this.apiBaseUrl,
    this.onUnauthorized,
  });

  // Get auth token from SharedPreferences
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Check internet connectivity
  Future<bool> _hasInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  // Get authorization headers with token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _getAuthToken();
    final headers = {'Content-Type': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Check if sync is needed (token exists and internet available)
  Future<bool> shouldSync() async {
    final token = await _getAuthToken();
    final hasInternet = await _hasInternetConnection();
    return token != null && hasInternet;
  }

  // Handle HTTP response and check for authentication errors
  void _handleHttpResponse(http.Response response, String operation) {
    if (response.statusCode == 401 || response.statusCode == 403) {
      print('Authentication failed for $operation: ${response.statusCode}');
      // Clear auth data and trigger logout callback
      _handleUnauthorized();
      throw UnauthorizedException(
        'Authentication failed: ${response.statusCode}',
      );
    }

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Failed to $operation: ${response.statusCode}');
    }
  }

  // Handle unauthorized access
  Future<void> _handleUnauthorized() async {
    try {
      // Clear authentication data
      final authService = AuthService(
        dbHelper: dbHelper,
        apiBaseUrl: apiBaseUrl,
      );
      await authService.logout();

      // Trigger callback if provided
      if (onUnauthorized != null) {
        onUnauthorized!();
      }
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  // Main sync function that syncs all master data in order
  Future<SyncResult> syncAllMasterData() async {
    try {
      if (!await shouldSync()) {
        return SyncResult(
          success: false,
          message: 'No internet connection or authentication token',
        );
      }

      // Sync in the specified order
      await syncBranches();
      await syncTimbangan();
      await syncTechnicians();
      await syncAnakTimbang();
      await syncStandards();

      return SyncResult(
        success: true,
        message: 'All master data synced successfully',
      );
    } catch (e) {
      return SyncResult(
        success: false,
        message: 'Sync failed: ${e.toString()}',
      );
    }
  }

  // Sync master branches from /branches
  Future<void> syncBranches() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('$apiBaseUrl/branches'),
      headers: headers,
    );

    _handleHttpResponse(response, 'sync branches');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final branches = data['data'] as List;

      final db = await dbHelper.database;
      int processedCount = 0;
      int skippedCount = 0;

      for (final branchData in branches) {
        final uuid = branchData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          skippedCount++;
          continue;
        }
        await _upsertBranch(db, branchData);
        processedCount++;
      }

      print(
        'Branches sync completed: $processedCount processed, $skippedCount skipped',
      );
    } else {
      throw Exception('Failed to sync branches: ${response.statusCode}');
    }
  }

  // Sync master timbangan from /timbangans
  Future<void> syncTimbangan() async {
    final headers = await _getAuthHeaders();

    // upload unsysnced timbangan
    final db = await dbHelper.database;
    final unsyncedTimbangans = await db.query(
      'master_timbangan',
      where: 'synced = ? OR synced IS NULL',
      whereArgs: [0],
    );

    if (unsyncedTimbangans.isNotEmpty) {
      for (final timbanganData in unsyncedTimbangans) {
        final uuid = timbanganData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          continue;
        }
        print("try to push unsynced timbangan: $uuid");
        print(timbanganData);
        final response = await http.post(
          Uri.parse('$apiBaseUrl/timbangans'),
          headers: headers,
          body: jsonEncode(timbanganData),
        );

        _handleHttpResponse(response, 'upload timbangan');

        if (response.statusCode == 201) {
          print('upload timbangan sukses');
          await db.update(
            'master_timbangan',
            {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
            where: 'uuid = ?',
            whereArgs: [uuid],
          );
        } else {
          print('Failed to upload timbangan: ${response.statusCode}');
          throw Exception('Failed to upload timbangan: ${response.statusCode}');
        }
      }
    }

    final response = await http.get(
      Uri.parse('$apiBaseUrl/timbangans'),
      headers: headers,
    );

    _handleHttpResponse(response, 'sync timbangans');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final timbangans = data['data'] as List;

      final db = await dbHelper.database;
      int processedCount = 0;
      int skippedCount = 0;

      for (final timbanganData in timbangans) {
        final uuid = timbanganData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          skippedCount++;
          continue;
        }
        await _upsertTimbangan(db, timbanganData);
        processedCount++;
      }

      print(
        'Timbangan sync completed: $processedCount processed, $skippedCount skipped',
      );
    } else {
      throw Exception('Failed to sync timbangan: ${response.statusCode}');
    }
  }

  // Sync master technicians from /technicians
  Future<void> syncTechnicians() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('$apiBaseUrl/technicians'),
      headers: headers,
    );

    _handleHttpResponse(response, 'sync technicians');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final technicians = data['data'] as List;

      final db = await dbHelper.database;
      int processedCount = 0;
      int skippedCount = 0;

      for (final technicianData in technicians) {
        final uuid = technicianData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          skippedCount++;
          continue;
        }
        await _upsertTechnician(db, technicianData);
        processedCount++;
      }

      print(
        'Technicians sync completed: $processedCount processed, $skippedCount skipped',
      );
    } else {
      throw Exception('Failed to sync technicians: ${response.statusCode}');
    }
  }

  // Sync master anak timbang from /anak-timbangs
  Future<void> syncAnakTimbang() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('$apiBaseUrl/anak-timbangs'),
      headers: headers,
    );

    _handleHttpResponse(response, 'sync anak timbangs');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final anakTimbangs = data['data'] as List;

      final db = await dbHelper.database;
      int processedCount = 0;
      int skippedCount = 0;

      for (final anakTimbangData in anakTimbangs) {
        final uuid = anakTimbangData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          skippedCount++;
          continue;
        }
        await _upsertAnakTimbang(db, anakTimbangData);
        processedCount++;
      }

      print(
        'Anak Timbang sync completed: $processedCount processed, $skippedCount skipped',
      );
    } else {
      throw Exception('Failed to sync anak timbang: ${response.statusCode}');
    }
  }

  // Sync master standards from /standards
  Future<void> syncStandards() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('$apiBaseUrl/standards'),
      headers: headers,
    );

    _handleHttpResponse(response, 'sync standards');

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final standards = data['data'] as List;

      final db = await dbHelper.database;
      int processedCount = 0;
      int skippedCount = 0;

      for (final standardData in standards) {
        final uuid = standardData['uuid'];
        if (uuid == null || uuid.toString().trim().isEmpty) {
          skippedCount++;
          continue;
        }
        await _upsertStandard(db, standardData);
        processedCount++;
      }

      print(
        'Standards sync completed: $processedCount processed, $skippedCount skipped',
      );
    } else {
      throw Exception('Failed to sync standards: ${response.statusCode}');
    }
  }

  // Upsert branch data (insert if new, update if exists)
  Future<void> _upsertBranch(db, Map<String, dynamic> branchData) async {
    final uuid = branchData['uuid'];

    // Skip if UUID is null or empty
    if (uuid == null || uuid.toString().trim().isEmpty) {
      print('Skipping branch record with null/empty UUID: $branchData');
      return;
    }

    // Check if record exists
    final existing = await db.query(
      'master_branches',
      where: 'uuid = ?',
      whereArgs: [uuid],
      limit: 1,
    );

    final data = {
      'uuid': uuid,
      'branch_code': branchData['branch_code'],
      'branch_name': branchData['branch_name'],
      'active': branchData['active'] == true ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      // Insert new record
      data['created_at'] =
          branchData['created_at'] ?? DateTime.now().toIso8601String();
      await db.insert('master_branches', data);
    } else {
      // Update existing record
      await db.update(
        'master_branches',
        data,
        where: 'uuid = ?',
        whereArgs: [uuid],
      );
    }
  }

  // Upsert timbangan data
  Future<void> _upsertTimbangan(db, Map<String, dynamic> timbanganData) async {
    final uuid = timbanganData['uuid'];

    // Skip if UUID is null or empty
    if (uuid == null || uuid.toString().trim().isEmpty) {
      print('Skipping timbangan record with null/empty UUID: $timbanganData');
      return;
    }

    final existing = await db.query(
      'master_timbangan',
      where: 'uuid = ?',
      whereArgs: [uuid],
      limit: 1,
    );

    final data = {
      'uuid': uuid,
      'id_timbangan': timbanganData['id_timbangan'],
      'timbangan_type': timbanganData['timbangan_type'],
      'brand': timbanganData['brand'],
      'model': timbanganData['model'],
      'snr': timbanganData['snr'],
      'capacity': timbanganData['capacity'],
      'location': timbanganData['location'],
      'branch_id': timbanganData['branch_id'],
      'synced': 1,
      'sync_time': DateTime.now().toIso8601String(),
      'active': timbanganData['active'] == true ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      data['created_at'] =
          timbanganData['created_at'] ?? DateTime.now().toIso8601String();
      await db.insert('master_timbangan', data);
    } else {
      await db.update(
        'master_timbangan',
        data,
        where: 'uuid = ?',
        whereArgs: [uuid],
      );
    }
  }

  // Upsert technician data
  Future<void> _upsertTechnician(
    db,
    Map<String, dynamic> technicianData,
  ) async {
    final uuid = technicianData['uuid'];

    // Skip if UUID is null or empty
    if (uuid == null || uuid.toString().trim().isEmpty) {
      print('Skipping technician record with null/empty UUID: $technicianData');
      return;
    }

    final existing = await db.query(
      'master_technicians',
      where: 'uuid = ?',
      whereArgs: [uuid],
      limit: 1,
    );

    final data = {
      'uuid': uuid,
      'tech_name': technicianData['tech_name'],
      'tech_initial': technicianData['tech_initial'],
      'branch_id': technicianData['branch_id'],
      'active': technicianData['active'] == true ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      data['created_at'] =
          technicianData['created_at'] ?? DateTime.now().toIso8601String();
      await db.insert('master_technicians', data);
    } else {
      await db.update(
        'master_technicians',
        data,
        where: 'uuid = ?',
        whereArgs: [uuid],
      );
    }
  }

  // Upsert anak timbang data
  Future<void> _upsertAnakTimbang(
    db,
    Map<String, dynamic> anakTimbangData,
  ) async {
    final uuid = anakTimbangData['uuid'];

    // Skip if UUID is null or empty
    if (uuid == null || uuid.toString().trim().isEmpty) {
      print(
        'Skipping anak timbang record with null/empty UUID: $anakTimbangData',
      );
      return;
    }

    final existing = await db.query(
      'master_anak_timbang',
      where: 'uuid = ?',
      whereArgs: [uuid],
      limit: 1,
    );

    final data = {
      'uuid': uuid,
      'no_invent': anakTimbangData['no_invent'],
      'branch_id': anakTimbangData['branch_id'],
      'value': anakTimbangData['value'] is String
          ? anakTimbangData['value']
          : jsonEncode(anakTimbangData['value']),
      'active': anakTimbangData['active'] == true ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      data['created_at'] =
          anakTimbangData['created_at'] ?? DateTime.now().toIso8601String();
      await db.insert('master_anak_timbang', data);
    } else {
      await db.update(
        'master_anak_timbang',
        data,
        where: 'uuid = ?',
        whereArgs: [uuid],
      );
    }
  }

  // Upsert standard data
  Future<void> _upsertStandard(db, Map<String, dynamic> standardData) async {
    final uuid = standardData['uuid'];

    // Skip if UUID is null or empty
    if (uuid == null || uuid.toString().trim().isEmpty) {
      print('Skipping standard record with null/empty UUID: $standardData');
      return;
    }

    final existing = await db.query(
      'master_standard',
      where: 'uuid = ?',
      whereArgs: [uuid],
      limit: 1,
    );

    final data = {
      'uuid': uuid,
      'no_invent': standardData['no_invent'],
      'branch_id': standardData['branch_id'],
      'value': standardData['value'] is String
          ? standardData['value']
          : jsonEncode(standardData['value']),
      'active': standardData['active'] == true ? 1 : 0,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (existing.isEmpty) {
      data['created_at'] =
          standardData['created_at'] ?? DateTime.now().toIso8601String();
      await db.insert('master_standard', data);
    } else {
      await db.update(
        'master_standard',
        data,
        where: 'uuid = ?',
        whereArgs: [uuid],
      );
    }
  }
}

// Result class for sync operations
class SyncResult {
  final bool success;
  final String message;

  SyncResult({required this.success, required this.message});
}
