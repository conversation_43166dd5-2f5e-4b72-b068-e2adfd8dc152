import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../helper/database.dart';
import '../helper/model.dart';

class AuthService {
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserUuid = 'user_uuid';
  static const String _keyUserId = 'user_id';
  static const String _keyUsername = 'username';
  static const String _keyUserFullName = 'user_full_name';
  static const String _keyUserInitialName = 'user_initial_name';
  static const String _keyUserBranchId = 'user_branch_id';
  static const String _keyUserBranchUuid = 'user_branch_uuid';
  static const String _keyUserBranchCode = 'user_branch_code';
  static const String _keyUserBranchName = 'user_branch_name';
  static const String _keyUserRole = 'user_role';
  static const String _keyAuthToken = 'auth_token';

  final DatabaseHelper _dbHelper;
  final String apiBaseUrl;

  AuthService({required DatabaseHelper dbHelper, required this.apiBaseUrl})
    : _dbHelper = dbHelper;

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  // Get current user info from SharedPreferences
  Future<Map<String, dynamic>?> getCurrentUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool(_keyIsLoggedIn) ?? false;

    if (!isLoggedIn) return null;

    return {
      'id': prefs.getInt(_keyUserId),
      'username': prefs.getString(_keyUsername),
      'role': prefs.getString(_keyUserRole),
    };
  }

  // Get stored auth token
  Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyAuthToken);
  }

  // Save auth token
  Future<void> _saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyAuthToken, token);
  }

  // Login method using API
  Future<LoginResult> login(String username, String password) async {
    try {
      final loginUrl = '$apiBaseUrl/auth/login';
      print('Making login request to: $loginUrl'); // Debug

      // Make API request to login endpoint
      final response = await http.post(
        Uri.parse(loginUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'username': username, 'password': password}),
      );

      print('Response status code: ${response.statusCode}'); // Debug
      print('Response body: ${response.body}'); // Debug

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // Check if login was successful
        if (responseData['success'] == true) {
          final token = responseData['data']['token'];
          final userData = responseData['data']['user'];

          // Create user object from API response
          final user = User(
            uuid: userData['uuid'],
            id: userData['id'],
            username: userData['username'],
            email: '', // Not provided in the response
            passwordHash: '', // Not needed for API login
            fullName: userData['technicianName'],
            initialName: userData['technicianInitial'],
            branchId: userData['branch']['id'],
            branchUuid: userData['branch']['uuid'],
            branchCode: userData['branch']['branch_code'],
            branchName: userData['branch']['branch_name'],
            role:
                'user', // Default role, can be updated if provided in response
            active: true,
            createdAt: DateTime.parse(userData['created_at']),
          );

          // Save login state and token
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_keyIsLoggedIn, true);
          await prefs.setInt(_keyUserId, user.id!);
          await prefs.setString(_keyUsername, user.username);
          await prefs.setString(_keyUserRole, user.role);
          await prefs.setString(_keyUserUuid, user.uuid!);
          await prefs.setString(_keyUserFullName, user.fullName!);
          await prefs.setString(_keyUserInitialName, user.initialName!);
          await prefs.setInt(_keyUserBranchId, user.branchId!);
          await prefs.setString(_keyUserBranchUuid, user.branchUuid!);
          await prefs.setString(_keyUserBranchCode, user.branchCode!);
          await prefs.setString(_keyUserBranchName, user.branchName!);
          await _saveAuthToken(token);

          return LoginResult(
            success: true,
            message: responseData['message'] ?? 'Login successful',
            user: user,
          );
        } else {
          return LoginResult(
            success: false,
            message: responseData['message'] ?? 'Login failed',
          );
        }
      } else {
        // Handle HTTP error responses
        final responseData = jsonDecode(response.body);
        return LoginResult(
          success: false,
          message:
              responseData['message'] ?? 'Login failed: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('API login failed: $e'); // Debug

      // Fallback to local database authentication
      print('Attempting fallback to local database authentication'); // Debug
      try {
        return await _loginWithLocalDatabase(username, password);
      } catch (localError) {
        print('Local database login also failed: $localError'); // Debug
        return LoginResult(
          success: false,
          message: 'Login failed: API unreachable and local auth failed',
        );
      }
    }
  }

  // Fallback local database authentication
  Future<LoginResult> _loginWithLocalDatabase(
    String username,
    String password,
  ) async {
    final db = await _dbHelper.database;

    // Query user from database
    final List<Map<String, dynamic>> result = await db.query(
      'users',
      where: 'username = ? AND active = 1',
      whereArgs: [username],
      limit: 1,
    );

    if (result.isEmpty) {
      return LoginResult(success: false, message: 'User not found');
    }

    final userData = result.first;
    final storedPassword = userData['password_hash'];

    // Simple password check (in production, use proper hashing)
    if (storedPassword != password) {
      return LoginResult(success: false, message: 'Invalid password');
    }

    // Create user object
    final user = User.fromMap(userData);

    // Save login state (no token for local auth)
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsLoggedIn, true);
    await prefs.setInt(_keyUserId, user.id!);
    await prefs.setString(_keyUsername, user.username);
    await prefs.setString(_keyUserRole, user.role);

    return LoginResult(
      success: true,
      message: 'Login successful (local)',
      user: user,
    );
  }

  // Logout method
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyIsLoggedIn);
    await prefs.remove(_keyUserId);
    await prefs.remove(_keyUsername);
    await prefs.remove(_keyUserRole);
    await prefs.remove(_keyAuthToken);
  }

  // Get user by ID
  Future<User?> getUserById(int userId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> result = await db.query(
        'users',
        where: 'id = ? AND active = 1',
        whereArgs: [userId],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return User.fromMap(result.first);
    } catch (e) {
      return null;
    }
  }

  // Create new user (for admin functionality)
  Future<bool> createUser({
    required String username,
    required String email,
    required String password,
    String? fullName,
    int? branchId,
    String role = 'user',
  }) async {
    try {
      final db = await _dbHelper.database;

      // Check if username or email already exists
      final existing = await db.query(
        'users',
        where: 'username = ? OR email = ?',
        whereArgs: [username, email],
        limit: 1,
      );

      if (existing.isNotEmpty) {
        return false; // User already exists
      }

      // Insert new user
      await db.insert('users', {
        'username': username,
        'email': email,
        'password_hash': password, // In production, hash this properly
        'full_name': fullName,
        'branch_id': branchId,
        'role': role,
        'active': 1,
        'created_at': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      return false;
    }
  }
}

class LoginResult {
  final bool success;
  final String message;
  final User? user;

  LoginResult({required this.success, required this.message, this.user});
}
