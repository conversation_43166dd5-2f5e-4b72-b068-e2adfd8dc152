import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../helper/database.dart';
import '../helper/model.dart';

class CalibrationRepository {
  final DatabaseHelper dbHelper;
  final String apiBaseUrl;
  final String apiKey;

  CalibrationRepository({
    required this.dbHelper,
    required this.apiBaseUrl,
    required this.apiKey,
  });

  // Get auth token from SharedPreferences
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Get authorization headers with token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _getAuthToken();
    final headers = {'Content-Type': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    } else {
      // Fallback to API key if no token
      headers['Authorization'] = 'Bearer $apiKey';
    }

    return headers;
  }

  // Local database operations
  Future<int> insertCalibrationRequestLocal(CalibrationRequest request) async {
    final db = await dbHelper.database;
    final id = await db.insert('calibration_requests', request.toMap());
    return id;
  }

  Future<List<CalibrationRequest>> getCalibrationRequestsLocal() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_requests',
      where: 'active = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => CalibrationRequest.fromMap(map)).toList();
  }

  Future<CalibrationRequest?> getCalibrationRequestById(int id) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_requests',
      where: 'id = ? AND active = ?',
      whereArgs: [id, 1],
      limit: 1,
    );
    if (maps.isEmpty) return null;
    return CalibrationRequest.fromMap(maps.first);
  }

  Future<CalibrationRequest?> getCalibrationRequestByUuid(String uuid) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_requests',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    if (maps.isEmpty) return null;
    return CalibrationRequest.fromMap(maps.first);
  }

  Future<int> updateCalibrationRequestLocal(CalibrationRequest request) async {
    final db = await dbHelper.database;
    return await db.update(
      'calibration_requests',
      request.toMap(),
      where: 'id = ?',
      whereArgs: [request.id],
    );
  }

  Future<int> deleteCalibrationRequestLocal(int id) async {
    final db = await dbHelper.database;
    return await db.delete(
      'calibration_requests',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<CalibrationRequest>> getUnsyncedRequests() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_requests',
      where: 'synced = ? AND active = ?',
      whereArgs: [0, 1],
    );
    return maps.map((map) => CalibrationRequest.fromMap(map)).toList();
  }

  // API operations
  Future<CalibrationRequest> createCalibrationRequestRemote(
    CalibrationRequest request,
  ) async {
    final headers = await _getAuthHeaders();
    final response = await http.post(
      Uri.parse('$apiBaseUrl/calibration-requests'),
      headers: headers,
      body: jsonEncode(request.toApiMap()),
    );

    if (response.statusCode == 201) {
      final responseData = jsonDecode(response.body);
      return CalibrationRequest.fromApiMap(responseData);
    } else {
      throw Exception(
        'Failed to create calibration request: ${response.statusCode}',
      );
    }
  }

  Future<List<CalibrationRequest>> getCalibrationRequestsRemote() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('$apiBaseUrl/calibration-requests'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final List<dynamic> responseData = jsonDecode(response.body);
      return responseData
          .map((data) => CalibrationRequest.fromApiMap(data))
          .toList();
    } else {
      throw Exception(
        'Failed to load calibration requests: ${response.statusCode}',
      );
    }
  }

  Future<void> syncCalibrationRequests() async {
    // Get unsynced requests from local database
    final unsyncedRequests = await getUnsyncedRequests();

    // Sync each request to the remote server
    for (final request in unsyncedRequests) {
      try {
        final syncedRequest = await createCalibrationRequestRemote(request);

        // Update local record with server response and mark as synced
        final db = await dbHelper.database;
        await db.update(
          'calibration_requests',
          {
            'synced': 1,
            'sync_time': DateTime.now().toIso8601String(),
            'id': syncedRequest.id, // Update with server-generated ID if needed
          },
          where: 'uuid = ?',
          whereArgs: [request.uuid],
        );
      } catch (e) {
        // Log error but continue with next request
        print('Failed to sync request ${request.uuid}: $e');
      }
    }

    // Optionally: Fetch latest data from server and update local database
    try {
      final remoteRequests = await getCalibrationRequestsRemote();
      final db = await dbHelper.database;

      // Batch insert or update local records
      final batch = db.batch();
      for (final request in remoteRequests) {
        batch.insert(
          'calibration_requests',
          request.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      await batch.commit();
    } catch (e) {
      print('Failed to update local database with remote data: $e');
    }
  }

  // related tables
  // Repeat Capacity methods
  Future<int> insertRepeatCapacityLocal(
    CalibrationRepeatCapacity capacity,
  ) async {
    final db = await dbHelper.database;
    return await db.insert('calibration_repeat_capacity', capacity.toMap());
  }

  Future<List<CalibrationRepeatCapacity>> getRepeatCapacitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_repeat_capacity',
      where: 'calibration_request_uuid = ? AND active = ?',
      whereArgs: [requestId, 1],
    );
    return maps.map((map) => CalibrationRepeatCapacity.fromMap(map)).toList();
  }

  Future<int> updateRepeatCapacityLocal(
    CalibrationRepeatCapacity capacity,
  ) async {
    final db = await dbHelper.database;
    return await db.update(
      'calibration_repeat_capacity',
      capacity.toMap(),
      where: 'id = ?',
      whereArgs: [capacity.id],
    );
  }

  // Linearity methods
  Future<int> insertLinearityLocal(CalibrationLinearity linearity) async {
    final db = await dbHelper.database;
    return await db.insert('calibration_linearity', linearity.toMap());
  }

  Future<List<CalibrationLinearity>> getLinearitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_linearity',
      where: 'calibration_request_uuid = ? AND active = ?',
      whereArgs: [requestId, 1],
    );
    return maps.map((map) => CalibrationLinearity.fromMap(map)).toList();
  }

  // Eccentricity methods
  Future<int> insertEccentricityLocal(
    CalibrationEccentricity eccentricity,
  ) async {
    final db = await dbHelper.database;
    return await db.insert('calibration_eccentricity', eccentricity.toMap());
  }

  Future<List<CalibrationEccentricity>> getEccentricitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_eccentricity',
      where: 'calibration_request_uuid = ? AND active = ?',
      whereArgs: [requestId, 1],
    );
    return maps.map((map) => CalibrationEccentricity.fromMap(map)).toList();
  }

  // Histerisys methods
  Future<int> insertHisterisysLocal(CalibrationHisterisys histerisys) async {
    final db = await dbHelper.database;
    return await db.insert('calibration_histerisys', histerisys.toMap());
  }

  Future<List<CalibrationHisterisys>> getHisterisysForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'calibration_histerisys',
      where: 'calibration_request_uuid = ? AND active = ?',
      whereArgs: [requestId, 1],
    );
    print("hysterisis request $requestId");
    print(maps);
    return maps.map((map) => CalibrationHisterisys.fromMap(map)).toList();
  }

  // API methods for syncing related tables
  Future<void> syncRelatedCalibrationData(String requestId) async {
    // Sync repeat capacities
    final localRepeatCapacities = await getRepeatCapacitiesForRequest(
      requestId,
    );
    for (final capacity in localRepeatCapacities) {
      try {
        await _syncRepeatCapacity(capacity);
      } catch (e) {
        print('Failed to sync repeat capacity ${capacity.id}: $e');
      }
    }

    // Sync linearities
    final localLinearities = await getLinearitiesForRequest(requestId);
    for (final linearity in localLinearities) {
      try {
        await _syncLinearity(linearity);
      } catch (e) {
        print('Failed to sync linearity ${linearity.id}: $e');
      }
    }

    // Sync eccentricities
    final localEccentricities = await getEccentricitiesForRequest(requestId);
    for (final eccentricity in localEccentricities) {
      try {
        await _syncEccentricity(eccentricity);
      } catch (e) {
        print('Failed to sync eccentricity ${eccentricity.id}: $e');
      }
    }

    // Sync histerisys
    final localHisterisys = await getHisterisysForRequest(requestId);
    for (final histerisys in localHisterisys) {
      try {
        await _syncHisterisys(histerisys);
      } catch (e) {
        print('Failed to sync histerisys ${histerisys.id}: $e');
      }
    }
  }

  Future<void> _syncRepeatCapacity(CalibrationRepeatCapacity capacity) async {
    final response = await http.post(
      Uri.parse('$apiBaseUrl/calibration-repeat-capacities'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(capacity.toApiMap()),
    );

    if (response.statusCode == 201) {
      final db = await dbHelper.database;
      await db.update(
        'calibration_repeat_capacity',
        {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [capacity.id],
      );
    } else {
      throw Exception('Failed to sync repeat capacity: ${response.statusCode}');
    }
  }

  Future<void> _syncLinearity(CalibrationLinearity linearity) async {
    final response = await http.post(
      Uri.parse('$apiBaseUrl/calibration-repeat-capacities'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(linearity.toApiMap()),
    );

    if (response.statusCode == 201) {
      final db = await dbHelper.database;
      await db.update(
        'calibration_linearity',
        {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [linearity.id],
      );
    } else {
      throw Exception(
        'Failed to sync repeat linearity: ${response.statusCode}',
      );
    }
  }

  Future<void> _syncEccentricity(CalibrationEccentricity eccentricity) async {
    final response = await http.post(
      Uri.parse('$apiBaseUrl/calibration-repeat-capacities'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(eccentricity.toApiMap()),
    );

    if (response.statusCode == 201) {
      final db = await dbHelper.database;
      await db.update(
        'calibration_eccentricity',
        {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [eccentricity.id],
      );
    } else {
      throw Exception(
        'Failed to sync repeat eccentricity: ${response.statusCode}',
      );
    }
  }

  Future<void> _syncHisterisys(CalibrationHisterisys histerisys) async {
    final response = await http.post(
      Uri.parse('$apiBaseUrl/calibration-repeat-capacities'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode(histerisys.toApiMap()),
    );

    if (response.statusCode == 201) {
      final db = await dbHelper.database;
      await db.update(
        'calibration_histerisys',
        {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [histerisys.id],
      );
    } else {
      throw Exception(
        'Failed to sync repeat histerisys: ${response.statusCode}',
      );
    }
  }

  // Alias methods for ExcelService compatibility
  Future<List<CalibrationRepeatCapacity>> getRepeatCapacitiesByRequestId(
    String requestId,
  ) async {
    return await getRepeatCapacitiesForRequest(requestId);
  }

  Future<List<CalibrationLinearity>> getLinearitiesByRequestId(
    String requestId,
  ) async {
    return await getLinearitiesForRequest(requestId);
  }

  Future<List<CalibrationEccentricity>> getEccentricitiesByRequestId(
    String requestId,
  ) async {
    return await getEccentricitiesForRequest(requestId);
  }

  Future<List<CalibrationHisterisys>> getHisterisysByRequestId(
    String requestId,
  ) async {
    return await getHisterisysForRequest(requestId);
  }

  // Master data methods
  Future<List<Map<String, dynamic>>> getMasterBranches() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_branches',
      where: 'active = ?',
      whereArgs: [1],
      orderBy: 'branch_name ASC',
    );
    return maps;
  }

  Future<List<Map<String, dynamic>>> getMasterTimbangan() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_timbangan',
      where: 'active = ? AND uuid is not null',
      whereArgs: [1],
      orderBy: 'id_timbangan ASC',
    );
    return maps;
  }

  Future<Map<String, dynamic>?> getTimbangan(String uuidTimbangan) async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_timbangan',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuidTimbangan, 1],
      limit: 1,
    );
    if (maps.isEmpty) return null;
    return maps.first;
  }

  Future<List<Map<String, dynamic>>> getMasterTechnicians() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_technicians',
      where: 'active = ?',
      whereArgs: [1],
      orderBy: 'tech_name ASC',
    );
    return maps;
  }

  Future<List<Map<String, dynamic>>> getMasterAnakTimbang() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_anak_timbang',
      where: 'active = ?',
      whereArgs: [1],
      orderBy: 'no_invent ASC',
    );
    return maps;
  }

  Future<List<Map<String, dynamic>>> getMasterStandard() async {
    final db = await dbHelper.database;
    final maps = await db.query(
      'master_standard',
      where: 'active = ?',
      whereArgs: [1],
      orderBy: 'no_invent ASC',
    );
    return maps;
  }

  // Get master data by UUID methods
  Future<Map<String, dynamic>?> getMasterBranchByUuid(String uuid) async {
    final db = await dbHelper.database;
    final results = await db.query(
      'master_branches',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<Map<String, dynamic>?> getMasterTimbanganByUuid(String uuid) async {
    final db = await dbHelper.database;
    final results = await db.query(
      'master_timbangan',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<Map<String, dynamic>?> getMasterTechnicianByUuid(String uuid) async {
    final db = await dbHelper.database;
    final results = await db.query(
      'master_technicians',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<Map<String, dynamic>?> getMasterAnakTimbanganByUuid(
    String uuid,
  ) async {
    final db = await dbHelper.database;
    final results = await db.query(
      'master_anak_timbang',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<Map<String, dynamic>?> getMasterStandardByUuid(String uuid) async {
    final db = await dbHelper.database;
    final results = await db.query(
      'master_standard',
      where: 'uuid = ? AND active = ?',
      whereArgs: [uuid, 1],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // Similar _syncLinearity, _syncEccentricity, _syncHisterisys methods would be implemented
}
