import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../helper/database.dart';
import '../helper/model.dart';

class CalibrationSyncService {
  final DatabaseHelper dbHelper;
  final String apiBaseUrl;

  CalibrationSyncService({required this.dbHelper, required this.apiBaseUrl});

  // Get auth token from SharedPreferences
  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Check internet connectivity
  Future<bool> _hasInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  }

  // Get authorization headers with token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _getAuthToken();
    final headers = {'Content-Type': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Check if sync is needed (token exists and internet available)
  Future<bool> shouldSync() async {
    final token = await _getAuthToken();
    final hasInternet = await _hasInternetConnection();
    return token != null && hasInternet;
  }

  // Main sync function
  Future<CalibrationSyncResult> syncCalibrationRequests() async {
    try {
      if (!await shouldSync()) {
        return CalibrationSyncResult(
          success: false,
          message: 'No internet connection or authentication token',
          uploadedCount: 0,
          downloadedCount: 0,
        );
      }

      // Step 1: Upload unsynced data to server
      final uploadResult = await _uploadUnsyncedData();

      // Step 2: Download data from server
      final downloadResult = await _downloadServerData();

      return CalibrationSyncResult(
        success: uploadResult.success && downloadResult.success,
        message: uploadResult.success && downloadResult.success
            ? 'Sync completed successfully'
            : 'Sync completed with errors: ${uploadResult.message}, ${downloadResult.message}',
        uploadedCount: uploadResult.uploadedCount,
        downloadedCount: downloadResult.downloadedCount,
      );
    } catch (e) {
      return CalibrationSyncResult(
        success: false,
        message: 'Sync failed: ${e.toString()}',
        uploadedCount: 0,
        downloadedCount: 0,
      );
    }
  }

  // Upload unsynced data to server
  Future<CalibrationSyncResult> _uploadUnsyncedData() async {
    try {
      final db = await dbHelper.database;

      // Get all unsynced calibration requests
      final unsyncedRequests = await db.query(
        'calibration_requests',
        where: 'synced = ? OR synced IS NULL',
        whereArgs: [0],
      );

      if (unsyncedRequests.isEmpty) {
        return CalibrationSyncResult(
          success: true,
          message: 'No unsynced data to upload',
          uploadedCount: 0,
          downloadedCount: 0,
        );
      }

      // Build calibrations array with related data
      final calibrations = <Map<String, dynamic>>[];

      for (final requestData in unsyncedRequests) {
        final requestId = requestData['uuid'] as String;
        final calibrationData = Map<String, dynamic>.from(requestData);

        // Get related data
        calibrationData['repeatCapacities'] =
            await _getRepeatCapacitiesForRequest(requestId);
        calibrationData['linearities'] = await _getLinearitiesForRequest(
          requestId,
        );
        calibrationData['eccentricities'] = await _getEccentricitiesForRequest(
          requestId,
        );
        calibrationData['histerisys'] = await _getHisterisysForRequest(
          requestId,
        );

        calibrations.add(calibrationData);
      }

      // Send to server
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$apiBaseUrl/calibrations/bulk-save'),
        headers: headers,
        body: jsonEncode({'calibrations': calibrations}),
      );

      if (response.statusCode == 200) {
        // Mark all uploaded requests as synced
        for (final requestData in unsyncedRequests) {
          await db.update(
            'calibration_requests',
            {'synced': 1, 'sync_time': DateTime.now().toIso8601String()},
            where: 'id = ?',
            whereArgs: [requestData['id']],
          );
        }

        return CalibrationSyncResult(
          success: true,
          message: 'Upload completed successfully',
          uploadedCount: unsyncedRequests.length,
          downloadedCount: 0,
        );
      } else {
        throw Exception(
          'Upload failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      return CalibrationSyncResult(
        success: false,
        message: 'Upload failed: ${e.toString()}',
        uploadedCount: 0,
        downloadedCount: 0,
      );
    }
  }

  // Get repeat capacities for a request
  Future<List<Map<String, dynamic>>> _getRepeatCapacitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    return await db.query(
      'calibration_repeat_capacity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
  }

  // Get linearities for a request
  Future<List<Map<String, dynamic>>> _getLinearitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    return await db.query(
      'calibration_linearity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
  }

  // Get eccentricities for a request
  Future<List<Map<String, dynamic>>> _getEccentricitiesForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    return await db.query(
      'calibration_eccentricity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
  }

  // Get histerisys for a request
  Future<List<Map<String, dynamic>>> _getHisterisysForRequest(
    String requestId,
  ) async {
    final db = await dbHelper.database;
    return await db.query(
      'calibration_histerisys',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
  }

  // Download data from server
  Future<CalibrationSyncResult> _downloadServerData() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$apiBaseUrl/calibrations'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final serverRequests = data['data'] as List;

        final db = await dbHelper.database;
        int downloadedCount = 0;

        for (final serverRequest in serverRequests) {
          final uuid = serverRequest['uuid'];
          if (uuid == null || uuid.toString().trim().isEmpty) {
            continue; // Skip records with null/empty UUID
          }

          // Check if record already exists locally
          final existing = await db.query(
            'calibration_requests',
            where: 'uuid = ?',
            whereArgs: [uuid],
            limit: 1,
          );

          if (existing.isEmpty) {
            // Insert new calibration request
            await _insertCalibrationRequestFromServer(serverRequest);
            downloadedCount++;
          } else {
            // Update existing record with server data
            await _updateCalibrationRequestFromServer(
              serverRequest,
              existing.first['uuid'] as String,
            );
          }
        }

        return CalibrationSyncResult(
          success: true,
          message: 'Download completed successfully',
          uploadedCount: 0,
          downloadedCount: downloadedCount,
        );
      } else {
        throw Exception(
          'Download failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      return CalibrationSyncResult(
        success: false,
        message: 'Download failed: ${e.toString()}',
        uploadedCount: 0,
        downloadedCount: 0,
      );
    }
  }

  // Insert calibration request from server
  Future<void> _insertCalibrationRequestFromServer(
    Map<String, dynamic> serverData,
  ) async {
    final db = await dbHelper.database;

    // Insert main calibration request
    final requestId = await db.insert('calibration_requests', {
      'uuid': serverData['uuid'],
      'order_number': serverData['order_number'],
      'company_name': serverData['company_name'],
      'company_address': serverData['company_address'],
      'contact_person': serverData['contact_person'],
      'cp_division': serverData['cp_division'],
      'certificate_pic': serverData['certificate_pic'],
      'cert_number': serverData['cert_number'],
      'timbangan_uuid': serverData['timbangan_uuid'],
      'branch_creator': serverData['branch_creator'],
      'technician_uuid': serverData['technician_uuid'],
      'preadjustment_anak_timbang_uuid':
          serverData['preadjustment_anak_timbang_uuid'],
      'preadjustment_nominal': serverData['preadjustment_nominal'],
      'preadjustment_value_1': serverData['preadjustment_value_1'],
      'preadjustment_value_2': serverData['preadjustment_value_2'],
      'preadjustment_value_3': serverData['preadjustment_value_3'],
      'preadjustment_value_4': serverData['preadjustment_value_4'],
      'standard_uuid': serverData['standard_uuid'],
      'daya_baca_r1_value': serverData['daya_baca_r1_value'],
      'daya_baca_r1_unit': serverData['daya_baca_r1_unit'],
      'daya_baca_r2_value': serverData['daya_baca_r2_value'],
      'daya_baca_r2_unit': serverData['daya_baca_r2_unit'],
      'daya_baca_r3_value': serverData['daya_baca_r3_value'],
      'daya_baca_r3_unit': serverData['daya_baca_r3_unit'],
      'accuration_percentage': serverData['accuration_percentage'],
      'before_temp': serverData['before_temp'],
      'before_humidity': serverData['before_humidity'],
      'before_barrometer': serverData['before_barrometer'],
      'after_temp': serverData['after_temp'],
      'after_humidity': serverData['after_humidity'],
      'after_barrometer': serverData['after_barrometer'],
      'all_metadata': serverData['all_metadata'],
      'status': serverData['status'],
      'synced': 1, // Mark as synced since it comes from server
      'sync_time': DateTime.now().toIso8601String(),
      'created_at':
          serverData['created_at'] ?? DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Insert related data if present
    if (serverData['repeatCapacities'] != null) {
      await _insertRelatedData(
        'calibration_repeat_capacity',
        serverData['repeatCapacities'],
        serverData['uuid'],
      );
    }
    if (serverData['linearities'] != null) {
      await _insertRelatedData(
        'calibration_linearity',
        serverData['linearities'],
        serverData['uuid'],
      );
    }
    if (serverData['eccentricities'] != null) {
      await _insertRelatedData(
        'calibration_eccentricity',
        serverData['eccentricities'],
        serverData['uuid'],
      );
    }
    if (serverData['histerisys'] != null) {
      await _insertRelatedData(
        'calibration_histerisys',
        serverData['histerisys'],
        serverData['uuid'],
      );
    }
  }

  // Update calibration request from server
  Future<void> _updateCalibrationRequestFromServer(
    Map<String, dynamic> serverData,
    String localId,
  ) async {
    final db = await dbHelper.database;

    // Update main calibration request
    await db.update(
      'calibration_requests',
      {
        'order_number': serverData['order_number'],
        'company_name': serverData['company_name'],
        'company_address': serverData['company_address'],
        'contact_person': serverData['contact_person'],
        'cp_division': serverData['cp_division'],
        'certificate_pic': serverData['certificate_pic'],
        'cert_number': serverData['cert_number'],
        'timbangan_uuid': serverData['timbangan_uuid'],
        'branch_creator': serverData['branch_creator'],
        'technician_uuid': serverData['technician_uuid'],
        'preadjustment_anak_timbang_uuid':
            serverData['preadjustment_anak_timbang_uuid'],
        'preadjustment_nominal': serverData['preadjustment_nominal'],
        'preadjustment_value_1': serverData['preadjustment_value_1'],
        'preadjustment_value_2': serverData['preadjustment_value_2'],
        'preadjustment_value_3': serverData['preadjustment_value_3'],
        'preadjustment_value_4': serverData['preadjustment_value_4'],
        'standard_uuid': serverData['standard_uuid'],
        'daya_baca_r1_value': serverData['daya_baca_r1_value'],
        'daya_baca_r1_unit': serverData['daya_baca_r1_unit'],
        'daya_baca_r2_value': serverData['daya_baca_r2_value'],
        'daya_baca_r2_unit': serverData['daya_baca_r2_unit'],
        'daya_baca_r3_value': serverData['daya_baca_r3_value'],
        'daya_baca_r3_unit': serverData['daya_baca_r3_unit'],
        'accuration_percentage': serverData['accuration_percentage'],
        'before_temp': serverData['before_temp'],
        'before_humidity': serverData['before_humidity'],
        'before_barrometer': serverData['before_barrometer'],
        'after_temp': serverData['after_temp'],
        'after_humidity': serverData['after_humidity'],
        'after_barrometer': serverData['after_barrometer'],
        'all_metadata': serverData['all_metadata'],
        'status': serverData['status'],
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'uuid = ?',
      whereArgs: [localId],
    );

    // Update related data (delete and re-insert for simplicity)
    await _deleteRelatedData(localId);

    if (serverData['repeatCapacities'] != null) {
      await _insertRelatedData(
        'calibration_repeat_capacity',
        serverData['repeatCapacities'],
        localId,
      );
    }
    if (serverData['linearities'] != null) {
      await _insertRelatedData(
        'calibration_linearity',
        serverData['linearities'],
        localId,
      );
    }
    if (serverData['eccentricities'] != null) {
      await _insertRelatedData(
        'calibration_eccentricity',
        serverData['eccentricities'],
        localId,
      );
    }
    if (serverData['histerisys'] != null) {
      await _insertRelatedData(
        'calibration_histerisys',
        serverData['histerisys'],
        localId,
      );
    }
  }

  // Insert related data for a calibration request
  Future<void> _insertRelatedData(
    String tableName,
    List<dynamic> data,
    String requestId,
  ) async {
    final db = await dbHelper.database;

    for (final item in data) {
      final itemData = Map<String, dynamic>.from(item);
      itemData['calibration_request_uuid'] = requestId;
      itemData['created_at'] =
          itemData['created_at'] ?? DateTime.now().toIso8601String();
      itemData['updated_at'] = DateTime.now().toIso8601String();

      await db.insert(tableName, itemData);
    }
  }

  // Delete related data for a calibration request
  Future<void> _deleteRelatedData(String requestId) async {
    final db = await dbHelper.database;

    await db.delete(
      'calibration_repeat_capacity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
    await db.delete(
      'calibration_linearity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
    await db.delete(
      'calibration_eccentricity',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
    await db.delete(
      'calibration_histerisys',
      where: 'calibration_request_uuid = ?',
      whereArgs: [requestId],
    );
  }
}

// Result class for calibration sync operations
class CalibrationSyncResult {
  final bool success;
  final String message;
  final int uploadedCount;
  final int downloadedCount;

  CalibrationSyncResult({
    required this.success,
    required this.message,
    required this.uploadedCount,
    required this.downloadedCount,
  });
}
