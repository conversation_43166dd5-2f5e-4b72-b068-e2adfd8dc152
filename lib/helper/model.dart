class User {
  final int? id;
  final String username;
  final String email;
  final String passwordHash;
  final String? fullName;
  final int? branchId;
  final String role;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  User({
    this.id,
    required this.username,
    required this.email,
    required this.passwordHash,
    this.fullName,
    this.branchId,
    this.role = 'user',
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'username': username,
      'email': email,
      'password_hash': passwordHash,
      'full_name': fullName,
      'branch_id': branchId,
      'role': role,
      'active': active ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      email: map['email'],
      passwordHash: map['password_hash'],
      fullName: map['full_name'],
      branchId: map['branch_id'],
      role: map['role'] ?? 'user',
      active: map['active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }
}

class Branch {
  final int id;
  final String branchCode;
  final String branchName;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Branch({
    required this.id,
    required this.branchCode,
    required this.branchName,
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });
}

class Technician {
  final int id;
  final String techName;
  final String techInitial;
  final int branchId;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Technician({
    required this.id,
    required this.techName,
    required this.techInitial,
    required this.branchId,
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });
}

class Timbangan {
  final int id;
  final String idTimbangan;
  final String timbanganType;
  final String brand;
  final String model;
  final String snr;
  final String capacity;
  final String location;
  final int branchId;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Timbangan({
    required this.id,
    required this.idTimbangan,
    required this.timbanganType,
    required this.brand,
    required this.model,
    required this.snr,
    required this.capacity,
    required this.location,
    required this.branchId,
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });
}

class AnakTimbang {
  final int id;
  final String noInvent;
  final int branchId;
  final String value;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AnakTimbang({
    required this.id,
    required this.noInvent,
    required this.branchId,
    required this.value,
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });
}

class Standard {
  final int id;
  final String noInvent;
  final int branchId;
  final String value;
  final bool active;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Standard({
    required this.id,
    required this.noInvent,
    required this.branchId,
    required this.value,
    this.active = true,
    required this.createdAt,
    this.updatedAt,
  });
}

class CalibrationRequest {
  final int? id;
  final String uuid;
  final String orderNumber;
  final String companyName;
  final String companyAddress;
  final String contactPerson;
  final String cpDivision;
  final String certNumber;
  final DateTime? dateCalibration;
  final String? technicianUuid;
  final String? timbanganUuid;
  final double? dayaBacaR1Value;
  final String? dayaBacaR1Unit;
  final double? dayaBacaR2Value;
  final String? dayaBacaR2Unit;
  final double? dayaBacaR3Value;
  final String? dayaBacaR3Unit;
  final double? accurationPercentage;
  final double? beforeTemp;
  final double? beforeHumidity;
  final double? beforeBarrometer;
  final double? afterTemp;
  final double? afterHumidity;
  final double? afterBarrometer;
  final String? standardUuid;
  final String? anakTimbangList;
  final String? preadjustmentAnakTimbangUuid;
  final double? preadjustmentNominal;
  final double? preadjustmentValue1;
  final double? preadjustmentValue2;
  final double? preadjustmentValue3;
  final double? preadjustmentValue4;
  final String? allMetadata;
  final String status;
  final String? certificatePic;
  final bool synced;
  final DateTime? syncTime;
  final bool active;
  final String? branchCreator;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CalibrationRequest({
    this.id,
    required this.uuid,
    required this.orderNumber,
    required this.companyName,
    required this.companyAddress,
    required this.contactPerson,
    required this.cpDivision,
    required this.certNumber,
    this.dateCalibration,
    this.technicianUuid,
    this.timbanganUuid,
    this.dayaBacaR1Value,
    this.dayaBacaR1Unit,
    this.dayaBacaR2Value,
    this.dayaBacaR2Unit,
    this.dayaBacaR3Value,
    this.dayaBacaR3Unit,
    this.accurationPercentage,
    this.beforeTemp,
    this.beforeHumidity,
    this.beforeBarrometer,
    this.afterTemp,
    this.afterHumidity,
    this.afterBarrometer,
    this.standardUuid,
    this.anakTimbangList,
    this.preadjustmentAnakTimbangUuid,
    this.preadjustmentNominal,
    this.preadjustmentValue1,
    this.preadjustmentValue2,
    this.preadjustmentValue3,
    this.preadjustmentValue4,
    this.allMetadata,
    this.status = 'cust-created',
    this.certificatePic,
    this.synced = false,
    this.syncTime,
    this.active = true,
    this.branchCreator,
    this.createdBy = 'system',
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'uuid': uuid,
      'order_number': orderNumber,
      'company_name': companyName,
      'company_address': companyAddress,
      'contact_person': contactPerson,
      'cp_division': cpDivision,
      'cert_number': certNumber,
      'date_calibration': dateCalibration?.toIso8601String(),
      'technician_uuid': technicianUuid,
      'timbangan_uuid': timbanganUuid,
      'daya_baca_r1_value': dayaBacaR1Value,
      'daya_baca_r1_unit': dayaBacaR1Unit,
      'daya_baca_r2_value': dayaBacaR2Value,
      'daya_baca_r2_unit': dayaBacaR2Unit,
      'daya_baca_r3_value': dayaBacaR3Value,
      'daya_baca_r3_unit': dayaBacaR3Unit,
      'accuration_percentage': accurationPercentage,
      'before_temp': beforeTemp,
      'before_humidity': beforeHumidity,
      'before_barrometer': beforeBarrometer,
      'after_temp': afterTemp,
      'after_humidity': afterHumidity,
      'after_barrometer': afterBarrometer,
      'standard_uuid': standardUuid,
      'anak_timbang_list': anakTimbangList,
      'preadjustment_anak_timbang_uuid': preadjustmentAnakTimbangUuid,
      'preadjustment_nominal': preadjustmentNominal,
      'preadjustment_value_1': preadjustmentValue1,
      'preadjustment_value_2': preadjustmentValue2,
      'preadjustment_value_3': preadjustmentValue3,
      'preadjustment_value_4': preadjustmentValue4,
      'all_metadata': allMetadata,
      'status': status,
      'certificate_pic': certificatePic,
      'synced': synced ? 1 : 0,
      'sync_time': syncTime?.toIso8601String(),
      'active': active ? 1 : 0,
      'branch_creator': branchCreator,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  Map<String, dynamic> toApiMap() {
    return {
      'uuid': uuid,
      'orderNumber': orderNumber,
      'companyName': companyName,
      'companyAddress': companyAddress,
      'contactPerson': contactPerson,
      'cpDivision': cpDivision,
      'certNumber': certNumber,
      'dateCalibration': dateCalibration?.toIso8601String(),
      'technicianUuid': technicianUuid,
      'timbanganUUid': timbanganUuid,
      'dayaBacaR1Value': dayaBacaR1Value,
      'dayaBacaR1Unit': dayaBacaR1Unit,
      'dayaBacaR2Value': dayaBacaR2Value,
      'dayaBacaR2Unit': dayaBacaR2Unit,
      'dayaBacaR3Value': dayaBacaR3Value,
      'dayaBacaR3Unit': dayaBacaR3Unit,
      'accurationPercentage': accurationPercentage,
      'beforeTemp': beforeTemp,
      'beforeHumidity': beforeHumidity,
      'beforeBarrometer': beforeBarrometer,
      'afterTemp': afterTemp,
      'afterHumidity': afterHumidity,
      'afterBarrometer': afterBarrometer,
      'standardUuid': standardUuid,
      'anakTimbangList': anakTimbangList,
      'preadjustmentAnakTimbangUuid': preadjustmentAnakTimbangUuid,
      'preadjustmentNominal': preadjustmentNominal,
      'preadjustmentValue1': preadjustmentValue1,
      'preadjustmentValue2': preadjustmentValue2,
      'preadjustmentValue3': preadjustmentValue3,
      'preadjustmentValue4': preadjustmentValue4,
      'allMetadata': allMetadata,
      'status': status,
      'certificatePic': certificatePic,
      'branchCreator': branchCreator,
    };
  }

  factory CalibrationRequest.fromMap(Map<String, dynamic> map) {
    return CalibrationRequest(
      id: map['id'],
      uuid: map['uuid'],
      orderNumber: map['order_number'],
      companyName: map['company_name'],
      companyAddress: map['company_address'],
      contactPerson: map['contact_person'],
      cpDivision: map['cp_division'],
      certNumber: map['cert_number'],
      dateCalibration: map['date_calibration'] != null
          ? DateTime.parse(map['date_calibration'])
          : null,
      technicianUuid: map['technician_uuid'],
      timbanganUuid: map['timbangan_uuid'],
      dayaBacaR1Value: map['daya_baca_r1_value'],
      dayaBacaR1Unit: map['daya_baca_r1_unit'],
      dayaBacaR2Value: map['daya_baca_r2_value'],
      dayaBacaR2Unit: map['daya_baca_r2_unit'],
      dayaBacaR3Value: map['daya_baca_r3_value'],
      dayaBacaR3Unit: map['daya_baca_r3_unit'],
      accurationPercentage: map['accuration_percentage'],
      beforeTemp: map['before_temp'],
      beforeHumidity: map['before_humidity'],
      beforeBarrometer: map['before_barrometer'],
      afterTemp: map['after_temp'],
      afterHumidity: map['after_humidity'],
      afterBarrometer: map['after_barrometer'],
      standardUuid: map['standard_uuid'],
      anakTimbangList: map['anak_timbang_list'],
      preadjustmentAnakTimbangUuid: map['preadjustment_anak_timbang_uuid'],
      preadjustmentNominal: map['preadjustment_nominal'],
      preadjustmentValue1: map['preadjustment_value_1'],
      preadjustmentValue2: map['preadjustment_value_2'],
      preadjustmentValue3: map['preadjustment_value_3'],
      preadjustmentValue4: map['preadjustment_value_4'],
      allMetadata: map['all_metadata'],
      status: map['status'],
      certificatePic: map['certificate_pic'],
      synced: map['synced'] == 1,
      syncTime: map['sync_time'] != null
          ? DateTime.parse(map['sync_time'])
          : null,
      active: map['active'] == 1,
      branchCreator: map['branch_creator'],
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }

  factory CalibrationRequest.fromApiMap(Map<String, dynamic> map) {
    return CalibrationRequest(
      id: map['id'],
      uuid: map['uuid'],
      orderNumber: map['orderNumber'],
      companyName: map['companyName'],
      companyAddress: map['companyAddress'],
      contactPerson: map['contactPerson'],
      cpDivision: map['cpDivision'],
      certNumber: map['certNumber'],
      dateCalibration: map['dateCalibration'] != null
          ? DateTime.parse(map['dateCalibration'])
          : null,
      technicianUuid: map['technicianUuid'],
      timbanganUuid: map['timbanganUuid'],
      dayaBacaR1Value: map['dayaBacaR1Value'],
      dayaBacaR1Unit: map['dayaBacaR1Unit'],
      dayaBacaR2Value: map['dayaBacaR2Value'],
      dayaBacaR2Unit: map['dayaBacaR2Unit'],
      dayaBacaR3Value: map['dayaBacaR3Value'],
      dayaBacaR3Unit: map['dayaBacaR3Unit'],
      accurationPercentage: map['accurationPercentage'],
      beforeTemp: map['beforeTemp'],
      beforeHumidity: map['beforeHumidity'],
      beforeBarrometer: map['beforeBarrometer'],
      afterTemp: map['afterTemp'],
      afterHumidity: map['afterHumidity'],
      afterBarrometer: map['afterBarrometer'],
      standardUuid: map['standardUuid'],
      anakTimbangList: map['anakTimbangList'],
      preadjustmentAnakTimbangUuid: map['preadjustmentAnakTimbangUuid'],
      preadjustmentNominal: map['preadjustmentNominal'],
      preadjustmentValue1: map['preadjustmentValue1'],
      preadjustmentValue2: map['preadjustmentValue2'],
      preadjustmentValue3: map['preadjustmentValue3'],
      preadjustmentValue4: map['preadjustmentValue4'],
      allMetadata: map['allMetadata'],
      status: map['status'],
      certificatePic: map['certificatePic'],
      synced: true,
      syncTime: DateTime.now(),
      active: map['active'] ?? true,
      branchCreator: map['branchCreator'],
      createdBy: map['createdBy'] ?? 'system',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : null,
    );
  }
}

class CalibrationRepeatCapacity {
  final int? id;
  final String calibrationRequestUuid;
  final String repeatType;
  final int rangeType;
  final double capacityNominal;
  final String capacityValue;
  final bool isPass;
  final bool active;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CalibrationRepeatCapacity({
    this.id,
    required this.calibrationRequestUuid,
    required this.repeatType,
    required this.rangeType,
    required this.capacityNominal,
    required this.capacityValue,
    this.isPass = false,
    this.active = true,
    this.createdBy = 'system',
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'calibration_request_uuid': calibrationRequestUuid,
      'repeat_type': repeatType,
      'range_type': rangeType,
      'capacity_nominal': capacityNominal,
      'capacity_value': capacityValue,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  Map<String, dynamic> toApiMap() {
    return {
      'id': id,
      'calibration_request_uuid': calibrationRequestUuid,
      'repeat_type': repeatType,
      'range_type': rangeType,
      'capacity_nominal': capacityNominal,
      'capacity_value': capacityValue,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CalibrationRepeatCapacity.fromMap(Map<String, dynamic> map) {
    return CalibrationRepeatCapacity(
      id: map['id'],
      calibrationRequestUuid: map['calibration_request_uuid'],
      repeatType: map['repeat_type'],
      rangeType: map['range_type'],
      capacityNominal: map['capacity_nominal'],
      capacityValue: map['capacity_value'],
      isPass: map['is_pass'] == 1,
      active: map['active'] == 1,
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }
}

class CalibrationLinearity {
  final int? id;
  final String calibrationRequestUuid;
  final double linearityNominal;
  final String linearityValue;
  final double reading1;
  final double reading2;
  final double reading3;
  final bool isPass;
  final bool active;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CalibrationLinearity({
    this.id,
    required this.calibrationRequestUuid,
    required this.linearityNominal,
    required this.linearityValue,
    required this.reading1,
    required this.reading2,
    required this.reading3,
    this.isPass = false,
    this.active = true,
    this.createdBy = 'system',
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'calibration_request_uuid': calibrationRequestUuid,
      'linearity_nominal': linearityNominal,
      'linearity_value': linearityValue,
      'reading_1': reading1,
      'reading_2': reading2,
      'reading_3': reading3,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  Map<String, dynamic> toApiMap() {
    return {
      'id': id,
      'calibration_request_uuid': calibrationRequestUuid,
      'linearity_nominal': linearityNominal,
      'linearity_value': linearityValue,
      'reading_1': reading1,
      'reading_2': reading2,
      'reading_3': reading3,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CalibrationLinearity.fromMap(Map<String, dynamic> map) {
    return CalibrationLinearity(
      id: map['id'],
      calibrationRequestUuid: map['calibration_request_uuid'],
      linearityNominal: map['linearity_nominal'],
      linearityValue: map['linearity_value'],
      reading1: map['reading_1'],
      reading2: map['reading_2'],
      reading3: map['reading_3'],
      isPass: map['is_pass'] == 1,
      active: map['active'] == 1,
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }
}

class CalibrationEccentricity {
  final int? id;
  final String calibrationRequestUuid;
  final double eccentricityNominal;
  final String eccentricityValue;
  final bool isPass;
  final bool active;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CalibrationEccentricity({
    this.id,
    required this.calibrationRequestUuid,
    required this.eccentricityNominal,
    required this.eccentricityValue,
    this.isPass = false,
    this.active = true,
    this.createdBy = 'system',
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'calibration_request_uuid': calibrationRequestUuid,
      'eccentricity_nominal': eccentricityNominal,
      'eccentricity_value': eccentricityValue,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  Map<String, dynamic> toApiMap() {
    return {
      'id': id,
      'calibration_request_uuid': calibrationRequestUuid,
      'eccentricity_nominal': eccentricityNominal,
      'eccentricity_value': eccentricityValue,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CalibrationEccentricity.fromMap(Map<String, dynamic> map) {
    return CalibrationEccentricity(
      id: map['id'],
      calibrationRequestUuid: map['calibration_request_uuid'],
      eccentricityNominal: map['eccentricity_nominal'],
      eccentricityValue: map['eccentricity_value'],
      isPass: map['is_pass'] == 1,
      active: map['active'] == 1,
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }
}

class CalibrationHisterisys {
  final int? id;
  final String calibrationRequestUuid;
  final double mNominal;
  final double m1Nominal;
  final String mValue;
  final String m1Value;
  final bool isPass;
  final bool active;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CalibrationHisterisys({
    this.id,
    required this.calibrationRequestUuid,
    required this.mNominal,
    required this.m1Nominal,
    required this.mValue,
    required this.m1Value,
    this.isPass = false,
    this.active = true,
    this.createdBy = 'system',
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    final map = {
      'calibration_request_uuid': calibrationRequestUuid,
      'm_nominal': mNominal,
      'm1_nominal': m1Nominal,
      'm_value': mValue,
      'm1_value': m1Value,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }

  Map<String, dynamic> toApiMap() {
    return {
      'id': id,
      'calibration_request_uuid': calibrationRequestUuid,
      'm_nominal': mNominal,
      'm1_nominal': m1Nominal,
      'm_value': mValue,
      'm1_value': m1Value,
      'is_pass': isPass ? 1 : 0,
      'active': active ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CalibrationHisterisys.fromMap(Map<String, dynamic> map) {
    return CalibrationHisterisys(
      id: map['id'],
      calibrationRequestUuid: map['calibration_request_uuid'],
      mNominal: map['m_nominal'],
      m1Nominal: map['m1_nominal'],
      mValue: map['m_value'],
      m1Value: map['m1_value'],
      isPass: map['is_pass'] == 1,
      active: map['active'] == 1,
      createdBy: map['created_by'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : null,
    );
  }
}
