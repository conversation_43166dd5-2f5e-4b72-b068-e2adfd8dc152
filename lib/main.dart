import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ekalibrasi/helper/database.dart';
import 'package:ekalibrasi/repositories/CalibrationRepository.dart';
import 'package:ekalibrasi/screens/AuthWrapper.dart';
import 'package:ekalibrasi/services/SyncService.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database helper
  final dbHelper = DatabaseHelper.instance;

  // Initialize repository
  final repository = CalibrationRepository(
    dbHelper: dbHelper,
    apiBaseUrl: 'https://labkalibrasi-almega.com/ekalibrasi-api/api',
    apiKey: 'your-api-key',
  );

  // Initialize connectivity
  final connectivity = Connectivity();

  // Initialize sync service
  final syncService = SyncService(
    repository: repository,
    connectivity: connectivity,
  );

  // Perform initial sync check
  // await syncService.checkAndSync();

  runApp(EkalibrasiApp(repository: repository, syncService: syncService));
}

class EkalibrasiApp extends StatelessWidget {
  final CalibrationRepository repository;
  final SyncService syncService;

  const EkalibrasiApp({
    super.key,
    required this.repository,
    required this.syncService,
  });

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ShadApp.custom(
      themeMode: ThemeMode.light,
      theme: ShadThemeData(
        colorScheme: ShadColorScheme.fromName('red'),
        brightness: Brightness.light,
        // textTheme: ShadTextTheme.fromGoogleFont(GoogleFonts.roboto, d),
      ),
      appBuilder: (context) {
        return MaterialApp(
          // title: 'Ekalibrasi App',
          theme: Theme.of(context),
          home: AuthWrapper(repository: repository, syncService: syncService),
          // home: TablePage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class MyWidget extends StatelessWidget {
  const MyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
