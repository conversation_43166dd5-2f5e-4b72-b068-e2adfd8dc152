import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import '../repositories/CalibrationRepository.dart';

class SyncService {
  final CalibrationRepository repository;
  final Connectivity connectivity;

  SyncService({required this.repository, required this.connectivity});

  // Check connectivity and sync if online
  Future<void> checkAndSync() async {
    final connectivityResult = await connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      await repository.syncCalibrationRequests();
    }
  }

  // Set up a listener for connectivity changes
  void setupConnectivityListener(BuildContext context) {
    connectivity.onConnectivityChanged.listen((result) {
      if (result != ConnectivityResult.none) {
        repository
            .syncCalibrationRequests()
            .then((_) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data synchronized successfully')),
              );
            })
            .catchError((error) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('Sync failed: $error')));
            });
      }
    });
  }
}
