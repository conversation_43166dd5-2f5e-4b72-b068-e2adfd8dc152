import 'dart:convert';
import 'dart:math' as math;

import 'package:ekalibrasi/helper/extension.dart';
import 'package:ekalibrasi/helper/model.dart';
import 'package:ekalibrasi/repositories/CalibrationRepository.dart';
import 'package:ekalibrasi/services/ExcelService.dart';
import 'package:ekalibrasi/services/CalibrationSyncService.dart';
import 'package:ekalibrasi/services/MasterDataSyncService.dart';
import 'package:ekalibrasi/widgets/repeat_capacity_card.dart';
import 'package:ekalibrasi/widgets/warning_alert.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class AddCalibrationRequest extends StatefulWidget {
  final CalibrationRepository repository;

  const AddCalibrationRequest({super.key, required this.repository});

  @override
  State<AddCalibrationRequest> createState() => _AddCalibrationRequestState();
}

class _AddCalibrationRequestState extends State<AddCalibrationRequest> {
  final histerisys = [
    {'label': 'M(p1)'},
    {'label': 'M+M\''},
    {'label': 'M(q1)'},
    {'label': 'Zero'},
    {'label': 'M+M\''},
    {'label': 'M(q2)'},
    {'label': 'Zero'},
    {'label': 'M(p2)'},
    {'label': 'M(p3)'},
    {'label': 'M+M\''},
    {'label': 'M(q3)'},
    {'label': 'Zero'},
    {'label': 'M+M\''},
    {'label': 'M(q4)'},
    {'label': 'Zero'},
    {'label': 'M(p4)'},
  ];

  final preadjustmentZM = [
    {'label': 'Z₁'},
    {'label': 'M₁'},
    {'label': 'M₂'},
    {'label': 'Z₂'},
  ];

  final repeatCapacityZ = [
    {'label': 'Z₁'},
    {'label': 'Z₂'},
    {'label': 'Z₃'},
    {'label': 'Z₄'},
    {'label': 'Z₅'},
    {'label': 'Z₆'},
    {'label': 'Z₇'},
    {'label': 'Z₈'},
    {'label': 'Z₉'},
    {'label': 'Z₁₀'},
  ];

  final repeatCapacityM = [
    {'label': 'M₁'},
    {'label': 'M₂'},
    {'label': 'M₃'},
    {'label': 'M₄'},
    {'label': 'M₅'},
    {'label': 'M₆'},
    {'label': 'M₇'},
    {'label': 'M₈'},
    {'label': 'M₉'},
    {'label': 'M₁₀'},
  ];

  final _formKey = GlobalKey<FormState>();
  // final _orderNumberController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _contactPersonController = TextEditingController();
  final _cpDivisionController = TextEditingController();
  final _certNumberController = TextEditingController();

  // Additional controllers for form fields
  // final _dateCalibrationController = TextEditingController();
  // final _technicianController = TextEditingController();
  final _timbanganIdController = TextEditingController();
  // final _dayaBacaR1ValueController = TextEditingController();
  // final _dayaBacaR1UnitController = TextEditingController();
  // final _dayaBacaR2ValueController = TextEditingController();
  // final _dayaBacaR2UnitController = TextEditingController();
  // final _accurationController = TextEditingController();
  // final _beforeTempController = TextEditingController();
  // final _beforeHumidityController = TextEditingController();
  // final _beforeBarrometerController = TextEditingController();
  // final _afterTempController = TextEditingController();
  // final _afterHumidityController = TextEditingController();
  // final _afterBarrometerController = TextEditingController();

  // Branch Selection
  String _selectedBranch = '';
  int _selectedBranchId = 0;

  // Data Timbangan Section
  DateTime? _selectedCalibrationDate;
  String _selectedTechnician = '';
  final TextEditingController _orderNumberController = TextEditingController();
  String _selectedScaleId = '';
  final TextEditingController _dayaBacaR1ValueController =
      TextEditingController();
  final TextEditingController _dayaBacaR2ValueController =
      TextEditingController();
  final TextEditingController _dayaBacaR3ValueController =
      TextEditingController();

  // Unit selection for daya baca (separate for R1 and R2)
  String _selectedDayaBacaR1Unit = 'g';
  String _selectedDayaBacaR2Unit = 'g';
  String _selectedDayaBacaR3Unit = 'g';
  final List<String> _dayaBacaUnits = ['kg', 'g', 'mg'];

  // Daya baca value selections (dropdown or manual input)
  String _selectedDayaBacaR1Value = '0.01';
  String _selectedDayaBacaR2Value = '0.01';
  String _selectedDayaBacaR3Value = '0.01';

  // Manual input flags
  bool _isManualInputR1 = false;
  bool _isManualInputR2 = false;
  bool _isManualInputR3 = false;

  // Timbangan input mode toggle
  bool _isAddNewTimbangan = false;

  // New timbangan input controllers
  final TextEditingController _newTimbanganIdController =
      TextEditingController();
  final TextEditingController _newTimbanganTypeController =
      TextEditingController();
  final TextEditingController _newTimbanganBrandController =
      TextEditingController();
  final TextEditingController _newTimbanganModelController =
      TextEditingController();
  final TextEditingController _newTimbanganSNRController =
      TextEditingController();
  final TextEditingController _newTimbanganCapacityController =
      TextEditingController();
  final TextEditingController _newTimbanganLocationController =
      TextEditingController();

  void _onR1UnitChanged(String newUnit) {
    setState(() {
      _selectedDayaBacaR1Unit = newUnit;
      // change also R2 R3
      _selectedDayaBacaR2Unit = newUnit;
      _selectedDayaBacaR3Unit = newUnit;
    });
  }

  void _addCapacityCard() {
    // Only allow one additional capacity card
    if (_additionalCapacityCards.length >= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only one additional capacity card is allowed'),
        ),
      );
      return;
    }

    setState(() {
      final cardIndex = _additionalCapacityCards.length;
      _additionalCapacityCards.add({
        'nominalController': TextEditingController(),
        'valueControllers1': List.generate(
          20,
          (index) => TextEditingController(),
        ),
        'valueControllers2': List.generate(
          20,
          (index) => TextEditingController(),
        ),
        'index': cardIndex,
      });

      // Add daya baca selections for this card (default to '1')
      _additionalCapacityHalfDayaBacaSelections.add('1');
      _additionalCapacityFullDayaBacaSelections.add('1');

      // Add listeners for the new controllers
      final card = _additionalCapacityCards.last;
      final nominalController =
          card['nominalController'] as TextEditingController;
      final valueControllers1 =
          card['valueControllers1'] as List<TextEditingController>;
      final valueControllers2 =
          card['valueControllers2'] as List<TextEditingController>;

      nominalController.addListener(() {
        setState(() {}); // Trigger rebuild to update yellow boxes
      });

      for (var controller in valueControllers1) {
        controller.addListener(() {
          setState(() {}); // Trigger rebuild to update right yellow boxes
        });
      }

      for (var controller in valueControllers2) {
        controller.addListener(() {
          setState(() {}); // Trigger rebuild to update right yellow boxes
        });
      }
    });
  }

  void _removeCapacityCard(int index) {
    setState(() {
      // Dispose controllers before removing
      final card = _additionalCapacityCards[index];
      (card['nominalController'] as TextEditingController).dispose();
      for (var controller
          in card['valueControllers1'] as List<TextEditingController>) {
        controller.dispose();
      }
      for (var controller
          in card['valueControllers2'] as List<TextEditingController>) {
        controller.dispose();
      }
      _additionalCapacityCards.removeAt(index);
      _additionalCapacityHalfDayaBacaSelections.removeAt(index);
      _additionalCapacityFullDayaBacaSelections.removeAt(index);
    });
  }

  Widget _buildAdditionalCapacityCard(
    Map<String, dynamic> cardData,
    int index,
  ) {
    final nominalController =
        cardData['nominalController'] as TextEditingController;
    final valueControllers1 =
        cardData['valueControllers1'] as List<TextEditingController>;
    final valueControllers2 =
        cardData['valueControllers2'] as List<TextEditingController>;

    return RepeatCapacityCard(
      title: 'Second Capacity',
      halfNominalValue: nominalController.text,
      fullNominalValue: nominalController.text,
      onHalfNominalChanged: (value) {
        nominalController.text = value;
      },
      onFullNominalChanged: (value) {
        nominalController.text = value;
      },
      valueControllers1: valueControllers1,
      valueControllers2: valueControllers2,
      selectedHalfDayaBaca: _additionalCapacityHalfDayaBacaSelections[index],
      selectedFullDayaBaca: _additionalCapacityFullDayaBacaSelections[index],
      onHalfDayaBacaChanged: (value) {
        setState(() {
          _additionalCapacityHalfDayaBacaSelections[index] = value;
        });
      },
      onFullDayaBacaChanged: (value) {
        setState(() {
          _additionalCapacityFullDayaBacaSelections[index] = value;
        });
      },
      dayaBacaR1Value: _dayaBacaR1ValueController.text,
      dayaBacaR2Value: _dayaBacaR2ValueController.text,
      dayaBacaR3Value: _dayaBacaR3ValueController.text,
      unitText: _selectedDayaBacaR2Unit, // Use daya baca R2 unit
      onRemove: () => _removeCapacityCard(index),
      showRemoveButton: true,
    );
  }

  final TextEditingController _accurationController = TextEditingController();

  // Before Section
  final TextEditingController _beforeTempController = TextEditingController();
  final TextEditingController _beforeHumidityController =
      TextEditingController();
  final TextEditingController _beforeBarrometerController =
      TextEditingController();
  String _selectedStandard = '';
  String? _selectedTimbangan;

  // Database data
  List<Map<String, dynamic>> _branches = [];
  List<Map<String, dynamic>> _timbangans = [];
  List<Map<String, dynamic>> _technicians = [];
  List<Map<String, dynamic>> _anakTimbangs = [];
  List<Map<String, dynamic>> _standards = [];
  Map<String, dynamic>? _selectedTimbanganData;

  // Timbangan detail display variables
  String _namaAlat = '';
  String _merk = '';
  String _type = '';
  String _snr = '';
  String _kapasitas = '';
  String _lokasi = '';

  List<TextEditingController> _weightSetControllers = List.generate(
    8,
    (index) => TextEditingController(),
  );

  // Pre-adjustment Section
  final TextEditingController _preAdjustmentNominalController =
      TextEditingController();
  List<TextEditingController> _preAdjustmentValueControllers = List.generate(
    4,
    (index) => TextEditingController(),
  );
  final TextEditingController _preAdjustmentIdController =
      TextEditingController();

  // Pre-adjustment daya baca selection
  String _selectedPreAdjustmentDayaBaca = '1'; // Default to Daya Baca 1

  // Repeat Half Capacity Section
  final TextEditingController _repeatHalfNominalController =
      TextEditingController();
  List<TextEditingController> _repeatHalfValueControllers1 = List.generate(
    10,
    (index) => TextEditingController(),
  );
  List<TextEditingController> _repeatHalfValueControllers2 = List.generate(
    10,
    (index) => TextEditingController(),
  );

  // Repeat Half Capacity daya baca selection
  String _selectedRepeatHalfDayaBaca = '1'; // Default to Daya Baca 1

  // Repeat Full Capacity Section
  final TextEditingController _repeatFullNominalController =
      TextEditingController();
  List<TextEditingController> _repeatFullValueControllers1 = List.generate(
    10,
    (index) => TextEditingController(),
  );
  List<TextEditingController> _repeatFullValueControllers2 = List.generate(
    10,
    (index) => TextEditingController(),
  );

  // Repeat Full Capacity daya baca selection
  String _selectedRepeatFullDayaBaca = '1'; // Default to Daya Baca 1

  // Additional Capacity Cards
  List<Map<String, dynamic>> _additionalCapacityCards = [];

  // Additional Capacity Cards daya baca selections
  final List<String> _additionalCapacityHalfDayaBacaSelections = [];
  final List<String> _additionalCapacityFullDayaBacaSelections = [];

  // Linearity Section
  final TextEditingController _linearityUnitController =
      TextEditingController();
  final List<TextEditingController> _linearityNominalControllers =
      List.generate(10, (index) => TextEditingController());
  final List<List<TextEditingController>> _linearityConventionalControllers =
      List.generate(
        10,
        (index) => List.generate(5, (index) => TextEditingController()),
      );
  final List<List<TextEditingController>> _linearityReadingControllers =
      List.generate(
        10,
        (index) => List.generate(3, (index) => TextEditingController()),
      );

  // Linearity daya baca selection
  String _selectedLinearityDayaBaca = '1'; // Default to Daya Baca 1

  // Eccentricity Section
  final TextEditingController _eccentricityNominalController =
      TextEditingController();
  final List<TextEditingController> _eccentricityValueControllers =
      List.generate(5, (index) => TextEditingController());
  String _selectedEccentricityDayaBaca = '1';

  // Histerisys Section
  final TextEditingController _histerisysMController = TextEditingController();
  final TextEditingController _histerisysMPrimeController =
      TextEditingController();
  List<TextEditingController> _histerisysValueControllers = List.generate(
    16,
    (index) => TextEditingController(),
  );
  String _selectedHisterisysDayaBaca = '1';

  // After Section
  final TextEditingController _afterTempController = TextEditingController();
  final TextEditingController _afterHumidityController =
      TextEditingController();
  final TextEditingController _afterBarrometerController =
      TextEditingController();

  // String _selectedBranch = '';
  // String _selectedStandard = '';
  String _status = 'cust-created';
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadMasterData();

    // Add listeners to update yellow box when values change
    _preAdjustmentNominalController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow box
    });

    _dayaBacaR1ValueController.addListener(() {
      if (_selectedPreAdjustmentDayaBaca == '1') {
        setState(() {}); // Trigger rebuild to update yellow box
      }
    });

    _dayaBacaR2ValueController.addListener(() {
      if (_selectedPreAdjustmentDayaBaca == '2') {
        setState(() {}); // Trigger rebuild to update yellow box
      }
    });

    _dayaBacaR3ValueController.addListener(() {
      if (_selectedPreAdjustmentDayaBaca == '3') {
        setState(() {}); // Trigger rebuild to update yellow box
      }
    });

    // Add listeners for pre-adjustment value controllers to update right yellow boxes
    for (var controller in _preAdjustmentValueControllers) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    // Add listeners for repeat half capacity controllers
    _repeatHalfNominalController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow boxes
    });

    for (var controller in _repeatHalfValueControllers1) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    for (var controller in _repeatHalfValueControllers2) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    // Add listeners for repeat full capacity controllers
    _repeatFullNominalController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow boxes
    });

    for (var controller in _repeatFullValueControllers1) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    for (var controller in _repeatFullValueControllers2) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    // Add listeners for eccentricity controllers
    _eccentricityNominalController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow boxes and PASS/FAIL
    });

    for (var controller in _eccentricityValueControllers) {
      controller.addListener(() {
        setState(
          () {},
        ); // Trigger rebuild to update right yellow boxes and PASS/FAIL
      });
    }

    // Add listener for accuration controller (affects eccentricity PASS/FAIL)
    _accurationController.addListener(() {
      setState(() {}); // Trigger rebuild to update eccentricity PASS/FAIL
    });

    // Add listener for pre-adjustment anak timbang selection (affects eccentricity)
    _preAdjustmentIdController.addListener(() {
      setState(() {}); // Trigger rebuild to update eccentricity PASS/FAIL
    });

    // Add listeners for histerisys controllers
    _histerisysMController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow boxes
    });

    _histerisysMPrimeController.addListener(() {
      setState(() {}); // Trigger rebuild to update yellow boxes
    });

    for (var controller in _histerisysValueControllers) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update right yellow boxes
      });
    }

    // Add listeners for linearity controllers (affects massa_konvensional display)
    for (var controller in _linearityNominalControllers) {
      controller.addListener(() {
        setState(() {}); // Trigger rebuild to update massa_konvensional display
      });
    }

    for (var list in _linearityConventionalControllers) {
      for (var controller in list) {
        controller.addListener(() {
          setState(
            () {},
          ); // Trigger rebuild to update massa_konvensional display
        });
      }
    }

    // Add listeners for linearity reading controllers (affects right yellow box)
    for (var list in _linearityReadingControllers) {
      for (var controller in list) {
        controller.addListener(() {
          setState(() {}); // Trigger rebuild to update right yellow boxes
        });
      }
    }
  }

  Future<void> _loadMasterData() async {
    try {
      final branches = await widget.repository.getMasterBranches();
      final timbangans = await widget.repository.getMasterTimbangan();
      final technicians = await widget.repository.getMasterTechnicians();
      final anakTimbangs = await widget.repository.getMasterAnakTimbang();
      final standards = await widget.repository.getMasterStandard();

      setState(() {
        _branches = branches;
        _timbangans = timbangans;
        _technicians = technicians;
        _anakTimbangs = anakTimbangs;
        _standards = standards;
      });

      // Prefill user data after loading master data
      await _prefillUserData();
    } catch (e) {
      print('Error loading master data: $e');
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load master data: $e')),
        );
      }
    }
  }

  // Helper methods for Weight Set / Anak Timbang display (for specific index)
  String _getWeightSetRangeCapacity(int index) {
    final selectedUuid = _weightSetControllers[index].text;

    if (selectedUuid.isEmpty) return '0'; // Default fallback

    final anakTimbang = _anakTimbangs.firstWhere(
      (at) => at['uuid'] == selectedUuid,
      orElse: () => <String, dynamic>{},
    );

    if (anakTimbang.isEmpty) return '0'; // Default fallback

    try {
      final valueJson = anakTimbang['value'] as String?;
      if (valueJson == null || valueJson.isEmpty) return '0';

      final List<dynamic> valueList = jsonDecode(valueJson);
      if (valueList.isEmpty) return '0';

      List<Map<String, dynamic>> weights = [];
      for (final item in valueList) {
        if (item is Map<String, dynamic>) {
          final massaStandardParsed = _parseMassaStandard(
            item['massa_standard'],
          );
          if (massaStandardParsed['value'] == null) {
            continue;
          }
          weights.add(item);
        }
      }

      if (weights.isEmpty) return '0';

      // Sort by massa_standard to get first and last
      weights.sort((a, b) {
        final massaAParsed = _parseMassaStandard(a['massa_standard']);
        final massaBParsed = _parseMassaStandard(b['massa_standard']);
        final massaA = massaAParsed['value'] as double? ?? 0.0;
        final massaB = massaBParsed['value'] as double? ?? 0.0;
        return massaA.compareTo(massaB);
      });

      final firstWeight = weights.first;
      final lastWeight = weights.last;

      final firstMassaParsed = _parseMassaStandard(
        firstWeight['massa_standard'],
      );
      final firstMassa = firstMassaParsed['value'] as double? ?? 0.0;
      final firstUnit = firstWeight['unit'] as String? ?? 'g';
      final lastMassaParsed = _parseMassaStandard(lastWeight['massa_standard']);
      final lastMassa = lastMassaParsed['value'] as double? ?? 0.0;
      final lastUnit = lastWeight['unit'] as String? ?? 'g';
      print("FS $firstMassa");
      print("LS $lastMassa");

      // Format first weight
      String firstDisplay = _formatWeightDisplay(firstMassa, firstUnit);
      String lastDisplay = _formatWeightDisplay(lastMassa, lastUnit);

      return '$firstDisplay - $lastDisplay';
    } catch (e) {
      print('Error formatting weight range: $e');
      return '0'; // Fallback on error
    }
  }

  String _getWeightSetClass(int index) {
    final selectedUuid = _weightSetControllers[index].text;

    if (selectedUuid.isEmpty) return '0'; // Default fallback

    final anakTimbang = _anakTimbangs.firstWhere(
      (at) => at['uuid'] == selectedUuid,
      orElse: () => <String, dynamic>{},
    );

    if (anakTimbang.isEmpty) return '0'; // Default fallback

    try {
      final valueJson = anakTimbang['value'] as String?;
      if (valueJson != null && valueJson.isNotEmpty) {
        final List<dynamic> valueList = jsonDecode(valueJson);
        if (valueList.isNotEmpty && valueList.first is Map<String, dynamic>) {
          final firstItem = valueList.first as Map<String, dynamic>;
          return firstItem['kelas'] as String? ?? '0';
        }
      }
    } catch (e) {
      print('Error parsing anak timbang class: $e');
    }

    return '0'; // Default fallback
  }

  // Helper method to parse numeric values from JSON (handles both String and num types)
  double? _parseNumericValue(dynamic value) {
    if (value == null) return null;

    if (value is num) {
      return value.toDouble();
    } else if (value is String) {
      return double.tryParse(value);
    }

    return null;
  }

  String _formatWeightDisplay(double massa, String unit) {
    try {
      // If massa is below 1 and unit is g or kg, convert to smaller unit
      if (massa < 1.0) {
        if (unit.toLowerCase() == 'g') {
          // Convert g to mg
          final massaInMg = massa * 1000;
          return '${massaInMg.toStringAsFixed(massaInMg == massaInMg.roundToDouble() ? 0 : 1)}mg';
        } else if (unit.toLowerCase() == 'kg') {
          // Convert kg to g
          final massaInG = massa * 1000;
          return '${massaInG.toStringAsFixed(massaInG == massaInG.roundToDouble() ? 0 : 1)}g';
        }
      } else if (massa >= 1000.0) {
        if (unit.toLowerCase() == 'g') {
          // Convert g to kg
          final massaInKg = massa / 1000;
          return '${massaInKg.toStringAsFixed(massaInKg == massaInKg.roundToDouble() ? 0 : 1)}kg';
        } else if (unit.toLowerCase() == 'mg') {
          // Convert mg to g
          final massaInG = massa / 1000;
          return '${massaInG.toStringAsFixed(massaInG == massaInG.roundToDouble() ? 0 : 1)}g';
        }
      }

      // Return as is with original unit
      return '${massa.toStringAsFixed(massa == massa.roundToDouble() ? 0 : 1)}$unit';
    } catch (e) {
      // Fallback in case of any formatting error
      return '${massa}$unit';
    }
  }

  // Prefill user data from SharedPreferences
  Future<void> _prefillUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final technicianUuid = prefs.getString('technician_uuid');
      final userBranchUuid = prefs.getString('user_branch_uuid');

      if (userBranchUuid != null && userBranchUuid.isNotEmpty) {
        // Check if the branch exists in the loaded branches
        final branchExists = _branches.any(
          (branch) => branch['uuid'] == userBranchUuid,
        );
        if (branchExists) {
          final branchSelected = _branches.firstWhere(
            (branch) => branch['uuid'] == userBranchUuid,
          );
          setState(() {
            _selectedBranch = userBranchUuid;
            _selectedBranchId = branchSelected['id'];
            print("selected branch id: ${_selectedBranchId}");
          });
        }
      }

      if (technicianUuid != null && technicianUuid.isNotEmpty) {
        // Check if the technician exists in the loaded technicians
        final technicianExists = _technicians.any(
          (technician) => technician['uuid'] == technicianUuid,
        );
        if (technicianExists) {
          setState(() {
            _selectedTechnician = technicianUuid;
          });
        }
      }
    } catch (e) {
      print('Error prefilling user data: $e');
      // Don't show error to user as this is not critical
    }
  }

  // Build timbangan input with switch toggle
  Widget _buildTimbanganInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Id Timbangan'),
            Row(
              children: [
                Text(
                  'Add New',
                  style: TextStyle(
                    fontSize: 12,
                    color: _isAddNewTimbangan ? Colors.blue : Colors.grey,
                  ),
                ),
                const SizedBox(width: 8),
                Switch(
                  value: _isAddNewTimbangan,
                  onChanged: (value) {
                    setState(() {
                      _isAddNewTimbangan = value;
                      if (value) {
                        // Clear selected timbangan when switching to add new
                        _selectedTimbangan = '';
                        _namaAlat = '';
                        _merk = '';
                        _type = '';
                        _snr = '';
                        _kapasitas = '';
                        _lokasi = '';
                      }
                    });
                  },
                  activeColor: Colors.blue,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 6),
        if (_isAddNewTimbangan)
          _buildNewTimbanganForm()
        else
          _buildSearchableTimbanganDropdown(),
      ],
    );
  }

  // Build custom searchable timbangan dropdown
  Widget _buildSearchableTimbanganDropdown() {
    return GestureDetector(
      onTap: () => _showTimbanganSearchDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                _selectedTimbangan != null && _selectedTimbangan != ''
                    ? _timbangans.firstWhere(
                            (t) => t['uuid'] == _selectedTimbangan,
                            orElse: () => {'id_timbangan': 'Unknown ID'},
                          )['id_timbangan'] ??
                          'Unknown ID'
                    : 'Pilih Timbangan',
                style: TextStyle(
                  color: _selectedTimbangan != null
                      ? Colors.black
                      : Colors.grey,
                ),
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  // Show searchable dialog for timbangan selection
  void _showTimbanganSearchDialog() {
    String searchQuery = '';
    List<Map<String, dynamic>> filteredTimbangans = List.from(_timbangans);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Pilih Timbangan'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search input
                    TextField(
                      decoration: const InputDecoration(
                        hintText: '🔍 Cari timbangan...',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      onChanged: (value) {
                        setDialogState(() {
                          searchQuery = value;
                          if (searchQuery.isEmpty) {
                            filteredTimbangans = List.from(_timbangans);
                          } else {
                            filteredTimbangans = _timbangans.where((timbangan) {
                              final idTimbangan =
                                  (timbangan['id_timbangan'] ?? '')
                                      .toString()
                                      .toLowerCase();
                              final brand = (timbangan['brand'] ?? '')
                                  .toString()
                                  .toLowerCase();
                              final model = (timbangan['model'] ?? '')
                                  .toString()
                                  .toLowerCase();
                              final query = searchQuery.toLowerCase();

                              return idTimbangan.contains(query) ||
                                  brand.contains(query) ||
                                  model.contains(query);
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    // Results list
                    Expanded(
                      child: filteredTimbangans.isEmpty
                          ? const Center(
                              child: Text(
                                'Tidak ada timbangan ditemukan',
                                style: TextStyle(color: Colors.grey),
                              ),
                            )
                          : ListView.builder(
                              itemCount: filteredTimbangans.length,
                              itemBuilder: (context, index) {
                                final timbangan = filteredTimbangans[index];
                                final isSelected =
                                    _selectedTimbangan == timbangan['uuid'];

                                return ListTile(
                                  title: Text(
                                    timbangan['id_timbangan'] ?? 'Unknown ID',
                                    style: TextStyle(
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  ),
                                  subtitle: Text(
                                    '${timbangan['brand'] ?? 'N/A'} - ${timbangan['model'] ?? 'N/A'}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  trailing: isSelected
                                      ? const Icon(
                                          Icons.check,
                                          color: Colors.green,
                                        )
                                      : null,
                                  onTap: () async {
                                    Navigator.of(context).pop();

                                    setState(() {
                                      _selectedTimbangan = timbangan['uuid'];
                                      _timbanganIdController.text =
                                          timbangan['uuid'];
                                    });

                                    // Load timbangan details
                                    try {
                                      final timbanganData = await widget
                                          .repository
                                          .getTimbangan(timbangan['uuid']);
                                      setState(() {
                                        _selectedTimbanganData = timbanganData;
                                        // Update the display variables
                                        if (timbanganData != null) {
                                          _namaAlat =
                                              timbanganData['timbangan_type'] ??
                                              'N/A';
                                          _merk =
                                              timbanganData['brand'] ?? 'N/A';
                                          _type =
                                              timbanganData['model'] ?? 'N/A';
                                          _snr = timbanganData['snr'] ?? 'N/A';
                                          _kapasitas =
                                              timbanganData['capacity'] ??
                                              'N/A';
                                          _lokasi =
                                              timbanganData['location'] ??
                                              'N/A';
                                        }
                                      });
                                    } catch (e) {
                                      print(
                                        'Error loading timbangan details: $e',
                                      );
                                    }
                                  },
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Batal'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Generic searchable dropdown builder
  Widget _buildSearchableDropdown({
    required String title,
    required String? selectedValue,
    required String placeholder,
    required List<Map<String, dynamic>> items,
    required String valueKey,
    required String displayKey,
    List<String> searchKeys = const [],
    String? subtitleKey,
    required Function(String) onSelected,
    bool enabled = true,
  }) {
    return GestureDetector(
      onTap: enabled
          ? () => _showSearchDialog(
              title: title,
              selectedValue: selectedValue,
              placeholder: placeholder,
              items: items,
              valueKey: valueKey,
              displayKey: displayKey,
              searchKeys: searchKeys.isNotEmpty ? searchKeys : [displayKey],
              subtitleKey: subtitleKey,
              onSelected: onSelected,
            )
          : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: enabled ? Colors.grey.shade300 : Colors.grey.shade200,
          ),
          borderRadius: BorderRadius.circular(8),
          color: enabled ? null : Colors.grey.shade50,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                selectedValue != null
                    ? items.firstWhere(
                            (item) => item[valueKey] == selectedValue,
                            orElse: () => {displayKey: placeholder},
                          )[displayKey] ??
                          placeholder
                    : placeholder,
                style: TextStyle(
                  color: enabled
                      ? (selectedValue != null ? Colors.black : Colors.grey)
                      : Colors.grey.shade400,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: enabled ? Colors.grey : Colors.grey.shade300,
            ),
          ],
        ),
      ),
    );
  }

  // Generic search dialog
  void _showSearchDialog({
    required String title,
    required String? selectedValue,
    required String placeholder,
    required List<Map<String, dynamic>> items,
    required String valueKey,
    required String displayKey,
    required List<String> searchKeys,
    String? subtitleKey,
    required Function(String) onSelected,
  }) {
    String searchQuery = '';
    List<Map<String, dynamic>> filteredItems = List.from(items);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(title),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: Column(
                  children: [
                    // Search input
                    TextField(
                      decoration: InputDecoration(
                        hintText: '🔍 Cari $title...',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      onChanged: (value) {
                        setDialogState(() {
                          searchQuery = value;
                          if (searchQuery.isEmpty) {
                            filteredItems = List.from(items);
                          } else {
                            filteredItems = items.where((item) {
                              final query = searchQuery.toLowerCase();
                              return searchKeys.any((key) {
                                final fieldValue = (item[key] ?? '')
                                    .toString()
                                    .toLowerCase();
                                return fieldValue.contains(query);
                              });
                            }).toList();
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    // Results list
                    Expanded(
                      child: filteredItems.isEmpty
                          ? Center(
                              child: Text(
                                'Tidak ada $title ditemukan',
                                style: const TextStyle(color: Colors.grey),
                              ),
                            )
                          : ListView.builder(
                              itemCount: filteredItems.length,
                              itemBuilder: (context, index) {
                                final item = filteredItems[index];
                                final isSelected =
                                    selectedValue == item[valueKey];

                                return ListTile(
                                  title: Text(
                                    item[displayKey]?.toString() ?? 'Unknown',
                                    style: TextStyle(
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  ),
                                  subtitle:
                                      subtitleKey != null &&
                                          item[subtitleKey] != null
                                      ? Text(
                                          item[subtitleKey].toString(),
                                          style: const TextStyle(fontSize: 12),
                                        )
                                      : null,
                                  trailing: isSelected
                                      ? const Icon(
                                          Icons.check,
                                          color: Colors.green,
                                        )
                                      : null,
                                  onTap: () {
                                    Navigator.of(context).pop();
                                    onSelected(item[valueKey].toString());
                                  },
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Batal'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Searchable Anak Timbang dropdown for weight set
  Widget _buildSearchableAnakTimbangDropdown(int index) {
    final selectedValue = _weightSetControllers[index].text.isEmpty
        ? null
        : _weightSetControllers[index].text;

    return _buildSearchableDropdown(
      title: 'Anak Timbang',
      selectedValue: selectedValue,
      placeholder: 'Pilih Anak Timbang',
      items: _anakTimbangs,
      valueKey: 'uuid',
      displayKey: 'no_invent',
      searchKeys: ['no_invent', 'brand', 'model'],
      subtitleKey: 'brand',
      onSelected: (value) {
        setState(() {
          _weightSetControllers[index].text = value;
          // Trigger rebuild to update yellow box display
        });
      },
    );
  }

  // Searchable Anak Timbang dropdown for pre-adjustment
  Widget _buildSearchablePreAdjustmentAnakTimbangDropdown() {
    final selectedValue = _preAdjustmentIdController.text.isEmpty
        ? null
        : _preAdjustmentIdController.text;

    return _buildSearchableDropdown(
      title: 'Anak Timbang',
      selectedValue: selectedValue,
      placeholder: 'Pilih Anak Timbang',
      items: _anakTimbangs,
      valueKey: 'uuid',
      displayKey: 'no_invent',
      searchKeys: ['no_invent', 'brand', 'model'],
      subtitleKey: 'brand',
      onSelected: (value) {
        setState(() {
          _preAdjustmentIdController.text = value;
        });
      },
    );
  }

  // Searchable Anak Timbang dropdown for linearity conventional
  Widget _buildSearchableLinearityAnakTimbangDropdown(int index, int index2) {
    final selectedValue =
        _linearityConventionalControllers[index][index2].text.isEmpty
        ? null
        : _linearityConventionalControllers[index][index2].text;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildSearchableDropdown(
                title: 'Anak Timbang',
                selectedValue: selectedValue,
                placeholder: 'Pilih AT',
                items: _anakTimbangs,
                valueKey: 'uuid',
                displayKey: 'no_invent',
                searchKeys: ['no_invent', 'brand', 'model'],
                subtitleKey: 'brand',
                onSelected: (value) {
                  // Check if nominal is already fulfilled before allowing selection
                  final remainingNominal = _calculateRemainingNominal(
                    index,
                    index2,
                  );

                  if (remainingNominal <= 0.0) {
                    // Show snackbar and prevent selection
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Nominal already fulfilled'),
                      ),
                    );
                    return; // Don't update the controller
                  }

                  setState(() {
                    _linearityConventionalControllers[index][index2].text =
                        value;
                  });
                },
              ),
            ),
            const SizedBox(width: 8),
            // Display massa_konvensional value on the right side
            Container(
              width: 80,
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                _getLinearityMassaKonvensionalDisplay(index, index2),
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.0),
      ],
    );
  }

  Widget _buildLinearityReading(int index, int index2) {
    return Row(
      children: [
        SizedBox(
          width: 80.0,
          child: ShadInput(
            controller: _linearityReadingControllers[index][index2],
            placeholder: Text('0'),
          ),
        ),
        Container(
          width: 120.0,
          padding: EdgeInsets.only(top: 10.0, bottom: 10.0),
          decoration: BoxDecoration(
            color: Colors.yellow,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10.0),
              bottomRight: Radius.circular(10.0),
            ),
          ),
          child: Center(
            child: Text(_formatLinearityRightYellowBox(index, index2)),
          ),
        ),
      ],
    );
  }

  // Get decimal digits from selected daya baca value
  int _getDecimalDigits(String dayaBacaValue) {
    if (dayaBacaValue.isEmpty) return 0;

    // Check if the value contains a decimal point
    if (!dayaBacaValue.contains('.')) return 0;

    // Get the decimal part and count digits
    final decimalPart = dayaBacaValue.split('.')[1];
    return decimalPart.length;
  }

  // Get the selected daya baca value based on selection
  String _getSelectedDayaBacaValue() {
    switch (_selectedPreAdjustmentDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  // Format the yellow box value with proper decimal digits
  String _formatYellowBoxValue([int? index]) {
    // For indices 0 or 3, use zero nominal
    final nominalText = (index != null && (index == 0 || index == 3))
        ? "0"
        : _preAdjustmentNominalController.text;

    if (nominalText.isEmpty) return "0,000";

    final nominalValue = double.tryParse(nominalText) ?? 0.0;
    final dayaBacaValue = _getSelectedDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Format with the appropriate decimal places
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Calculate the right yellow box value for pre-adjustment
  String _formatRightYellowBoxValue(int index) {
    // For indices 0 or 3, use zero nominal
    final nominalText = (index == 0 || index == 3)
        ? "0"
        : _preAdjustmentNominalController.text;
    final middleValueText = _preAdjustmentValueControllers[index].text;

    // Default to left side value if no middle value
    if (middleValueText.isEmpty) {
      return _formatYellowBoxValue(index);
    }

    // Parse values
    final nominalValue = double.tryParse(nominalText) ?? 0.0;
    final middleValue = double.tryParse(middleValueText) ?? 0.0;

    // Get decimal digits from selected daya baca
    final dayaBacaValue = _getSelectedDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Calculate the minimum decimal addition
    // For example: if decimalDigits = 2, then minimumDecimal = 0.01
    // if decimalDigits = 3, then minimumDecimal = 0.001
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    // Calculate: left side + (middle value * minimum decimal)
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);

    // Format with the appropriate decimal places
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Get the selected daya baca value for repeat half capacity
  String _getSelectedRepeatHalfDayaBacaValue() {
    switch (_selectedRepeatHalfDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  // Get the selected daya baca value for repeat full capacity
  String _getSelectedRepeatFullDayaBacaValue() {
    switch (_selectedRepeatFullDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  // Format the left yellow box value for repeat half capacity
  String _formatRepeatHalfLeftYellowBoxValue(int index) {
    // First 10 indices (0-9) use 0 nominal, next 10 (10-19) use filled nominal
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_repeatHalfNominalController.text) ?? 0.0);
    final dayaBacaValue = _getSelectedRepeatHalfDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Format with the appropriate decimal places
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the right yellow box value for repeat half capacity
  String _formatRepeatHalfRightYellowBoxValue(int index) {
    final middleValueText = index < 10
        ? _repeatHalfValueControllers1[index].text
        : _repeatHalfValueControllers2[index - 10].text;

    // Default to left side value if no middle value
    if (middleValueText.isEmpty) {
      return _formatRepeatHalfLeftYellowBoxValue(index);
    }

    // Parse values
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_repeatHalfNominalController.text) ?? 0.0);
    final middleValue = double.tryParse(middleValueText) ?? 0.0;

    // Get decimal digits from selected daya baca
    final dayaBacaValue = _getSelectedRepeatHalfDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Calculate the minimum decimal addition
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    // Calculate: left side + (middle value * minimum decimal)
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);

    // Format with the appropriate decimal places
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the left yellow box value for repeat full capacity
  String _formatRepeatFullLeftYellowBoxValue(int index) {
    // First 10 indices (0-9) use 0 nominal, next 10 (10-19) use filled nominal
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_repeatFullNominalController.text) ?? 0.0);
    final dayaBacaValue = _getSelectedRepeatFullDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Format with the appropriate decimal places
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Format the right yellow box value for repeat full capacity
  String _formatRepeatFullRightYellowBoxValue(int index) {
    final middleValueText = index < 10
        ? _repeatFullValueControllers1[index].text
        : _repeatFullValueControllers2[index - 10].text;

    // Default to left side value if no middle value
    if (middleValueText.isEmpty) {
      return _formatRepeatFullLeftYellowBoxValue(index);
    }

    // Parse values
    final nominalValue = index < 10
        ? 0.0
        : (double.tryParse(_repeatFullNominalController.text) ?? 0.0);
    final middleValue = double.tryParse(middleValueText) ?? 0.0;

    // Get decimal digits from selected daya baca
    final dayaBacaValue = _getSelectedRepeatFullDayaBacaValue();
    final decimalDigits = _getDecimalDigits(dayaBacaValue);

    // Calculate the minimum decimal addition
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    // Calculate: left side + (middle value * minimum decimal)
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);

    // Format with the appropriate decimal places
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    // Replace dot with comma for Indonesian format
    return formattedValue.replaceAll('.', ',');
  }

  // Calculate standard deviation for repeat capacity
  double _calculateStandardDeviation(List<double> values) {
    if (values.isEmpty) return 0.0;

    // Calculate mean
    double mean = values.reduce((a, b) => a + b) / values.length;

    // Calculate variance
    double variance =
        values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) /
        values.length;

    // Return standard deviation
    return math.sqrt(variance);
  }

  // Calculate PASS/FAIL for REPEAT Half Capacity
  Map<String, dynamic> _calculateRepeatHalfPassFail() {
    List<double> differences = [];

    // Calculate differences: right yellow box values of indices 10-19 minus 0-9
    for (int i = 0; i < 10; i++) {
      final rightValue0to9 = _parseRightYellowBoxValue(
        _formatRepeatHalfRightYellowBoxValue(i),
      );
      final rightValue10to19 = _parseRightYellowBoxValue(
        _formatRepeatHalfRightYellowBoxValue(i + 10),
      );
      differences.add(rightValue10to19 - rightValue0to9);
    }

    // Calculate standard deviation
    final standardDeviation = _calculateStandardDeviation(differences);

    // Get selected daya baca value for comparison
    final selectedDayaBacaValue =
        double.tryParse(_getSelectedRepeatHalfDayaBacaValue()) ?? 0.0;

    // Determine PASS/FAIL
    final isPass = standardDeviation <= selectedDayaBacaValue;

    return {
      'differences': differences,
      'standardDeviation': standardDeviation,
      'selectedDayaBacaValue': selectedDayaBacaValue,
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
    };
  }

  // Calculate PASS/FAIL for REPEAT Full Capacity
  Map<String, dynamic> _calculateRepeatFullPassFail() {
    List<double> differences = [];

    // Calculate differences: right yellow box values of indices 10-19 minus 0-9
    for (int i = 0; i < 10; i++) {
      final rightValue0to9 = _parseRightYellowBoxValue(
        _formatRepeatFullRightYellowBoxValue(i),
      );
      final rightValue10to19 = _parseRightYellowBoxValue(
        _formatRepeatFullRightYellowBoxValue(i + 10),
      );
      differences.add(rightValue10to19 - rightValue0to9);
    }

    // Calculate standard deviation
    final standardDeviation = _calculateStandardDeviation(differences);

    // Get selected daya baca value for comparison
    final selectedDayaBacaValue =
        double.tryParse(_getSelectedRepeatFullDayaBacaValue()) ?? 0.0;

    // Determine PASS/FAIL
    final isPass = standardDeviation <= selectedDayaBacaValue;

    return {
      'differences': differences,
      'standardDeviation': standardDeviation,
      'selectedDayaBacaValue': selectedDayaBacaValue,
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
    };
  }

  // Get PASS/FAIL result text for REPEAT Half Capacity
  String _getRepeatHalfPassFailText() {
    final result = _calculateRepeatHalfPassFail();
    return result['result'];
  }

  // Get PASS/FAIL result text for REPEAT Full Capacity
  String _getRepeatFullPassFailText() {
    final result = _calculateRepeatFullPassFail();
    return result['result'];
  }

  // Get PASS/FAIL color for REPEAT Half Capacity
  List<Color> _getRepeatHalfPassFailColorGradient() {
    final result = _calculateRepeatHalfPassFail();
    return result['isPass']
        ? [Color.fromARGB(255, 0, 212, 71), Color.fromARGB(255, 63, 181, 85)]
        : [Color.fromARGB(255, 212, 0, 0), Color.fromARGB(255, 181, 63, 63)];
  }

  // Get PASS/FAIL color for REPEAT Full Capacity
  List<Color> _getRepeatFullPassFailColorGradient() {
    final result = _calculateRepeatFullPassFail();
    return result['isPass']
        ? [Color.fromARGB(255, 0, 212, 71), Color.fromARGB(255, 63, 181, 85)]
        : [Color.fromARGB(255, 212, 0, 0), Color.fromARGB(255, 181, 63, 63)];
  }

  // Helper method to parse right yellow box value (convert "1999,996" to 1999.996)
  double _parseRightYellowBoxValue(String formattedValue) {
    // Replace comma with dot and parse as double
    return double.tryParse(formattedValue.replaceAll(',', '.')) ?? 0.0;
  }

  // Helper methods for eccentricity calculations
  int _getEccentricityDecimalDigits() {
    final dayaBacaValue = _getSelectedEccentricityDayaBacaValue();
    if (dayaBacaValue.isEmpty) return 0;
    if (!dayaBacaValue.contains('.')) return 0;
    final decimalPart = dayaBacaValue.split('.')[1];
    return decimalPart.length;
  }

  String _getSelectedEccentricityDayaBacaValue() {
    switch (_selectedEccentricityDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  String _formatEccentricityLeftYellowBox() {
    final nominalValue =
        double.tryParse(_eccentricityNominalController.text) ?? 0.0;
    final decimalDigits = _getEccentricityDecimalDigits();
    final formattedValue = nominalValue.toStringAsFixed(decimalDigits);
    return formattedValue.replaceAll('.', ',');
  }

  String _formatEccentricityRightYellowBox(int index) {
    final middleValueText = _eccentricityValueControllers[index].text;

    if (middleValueText.isEmpty) {
      return _formatEccentricityLeftYellowBox();
    }

    final nominalValue =
        double.tryParse(_eccentricityNominalController.text) ?? 0.0;
    final middleValue = double.tryParse(middleValueText) ?? 0.0;
    final decimalDigits = _getEccentricityDecimalDigits();
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);
    final calculatedValue = nominalValue + (middleValue * minimumDecimal);
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);
    return formattedValue.replaceAll('.', ',');
  }

  // Enhanced eccentricity calculation methods
  double? _getMassaKonvensional() {
    try {
      // Get the selected anak timbang UUID from pre-adjustment
      final selectedAnakTimbangUuid = _preAdjustmentIdController.text;
      if (selectedAnakTimbangUuid.isEmpty) return null;

      // Find the selected anak timbang in the list
      final selectedAnakTimbang = _anakTimbangs.firstWhere(
        (anakTimbang) => anakTimbang['uuid'] == selectedAnakTimbangUuid,
        orElse: () => <String, dynamic>{},
      );

      if (selectedAnakTimbang.isEmpty) return null;

      // Parse the JSON value column
      final valueJson = selectedAnakTimbang['value'] as String?;
      if (valueJson == null || valueJson.isEmpty) return null;

      final List<dynamic> valueList = jsonDecode(valueJson);

      // Get the nominal value from eccentricity section
      final eccentricityNominal = double.tryParse(
        _eccentricityNominalController.text,
      );
      if (eccentricityNominal == null) return null;

      // Find the matching massa_standard
      for (final item in valueList) {
        if (item is Map<String, dynamic>) {
          final massaStandardParsed = _parseMassaStandard(
            item['massa_standard'],
          );
          final massaStandard = massaStandardParsed['value'] as double?;
          if (massaStandard != null && massaStandard == eccentricityNominal) {
            return _parseMassaKonvensional(item['massa_konvensional']);
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  List<double> _calculateEccentricityDeviasi() {
    final massaKonvensional = _getMassaKonvensional();

    if (massaKonvensional == null) {
      return [];
    }

    List<double> deviasi = [];

    for (int i = 0; i < _eccentricityValueControllers.length; i++) {
      final rightYellowBoxFormatted = _formatEccentricityRightYellowBox(i);
      final rightYellowBoxValue = _parseRightYellowBoxValue(
        rightYellowBoxFormatted,
      );
      final deviation = massaKonvensional - rightYellowBoxValue;
      deviasi.add(deviation);
    }
    return deviasi;
  }

  double? _calculateMPE() {
    final massaKonvensional = _getMassaKonvensional();

    if (massaKonvensional == null) {
      return null;
    }

    final accurationText = _accurationController.text;
    final accuration = double.tryParse(accurationText);

    if (accuration == null) {
      return null;
    }

    final mpe = massaKonvensional * (accuration / 100 / 2);

    return mpe;
  }

  Map<String, dynamic> _calculateEccentricityPassFail() {
    final eccentricityDeviasi = _calculateEccentricityDeviasi();
    final mpe = _calculateMPE();

    if (eccentricityDeviasi.isEmpty || mpe == null) {
      return {
        'result': 'FAIL',
        'isPass': false,
        'deviasi': <double>[],
        'mpe': 0.0,
        'reason': 'Missing data or calculation error',
      };
    }

    // Check if all absolute deviations are less than MPE
    bool allPass = true;

    for (int i = 0; i < eccentricityDeviasi.length; i++) {
      final deviation = eccentricityDeviasi[i];
      final absDeviation = deviation.abs();
      final isWithinLimit = absDeviation < mpe;

      if (!isWithinLimit) {
        allPass = false;
      }
    }

    final result = allPass ? 'PASS' : 'FAIL';

    return {
      'result': result,
      'isPass': allPass,
      'deviasi': eccentricityDeviasi,
      'mpe': mpe,
      'reason': allPass
          ? 'All deviations are within MPE limit'
          : 'One or more deviations exceed MPE limit',
    };
  }

  String _getEccentricityPassFailText() {
    final result = _calculateEccentricityPassFail();
    return result['result'];
  }

  List<Color> _getEccentricityPassFailColorGradient() {
    final result = _calculateEccentricityPassFail();
    return result['isPass']
        ? [Color.fromARGB(255, 0, 212, 71), Color.fromARGB(255, 63, 181, 85)]
        : [Color.fromARGB(255, 212, 0, 0), Color.fromARGB(255, 181, 63, 63)];
  }

  // Helper methods for linearity anak timbang massa konvensional
  double? _findClosestMassaStandard(
    List<dynamic> valueList,
    double targetNominal,
  ) {
    double? closestMassaStandard;
    double? closestMassaKonvensional;
    double minDifference = double.infinity;

    // Get the current linearity daya baca unit
    final dayaBacaUnit = _getSelectedLinearityDayaBacaUnit();

    // Convert targetNominal to grams since massa_standard is stored in grams
    final targetNominalInGrams = _convertToGrams(targetNominal, dayaBacaUnit);

    print("Target Nominal: $targetNominal $dayaBacaUnit");
    print("Target Nominal in grams: $targetNominalInGrams g");

    for (final item in valueList) {
      if (item is Map<String, dynamic>) {
        final massaStandardParsed = _parseMassaStandard(item['massa_standard']);
        final massaStandard = massaStandardParsed['value'] as double?;
        final hasAsterisk = massaStandardParsed['hasAsterisk'] as bool;

        if (massaStandard == null) {
          continue;
        }

        final massaKonvensional = _parseMassaKonvensional(
          item['massa_konvensional'],
        );

        // print("Massa Standard: $massaStandard g${hasAsterisk ? ' (*)' : ''}");
        // print("Massa Konvensional: $massaKonvensional g");

        if (massaKonvensional != null) {
          // Compare massa_standard (in grams) with targetNominalInGrams
          final difference = (massaStandard - targetNominalInGrams).abs();
          if (difference < minDifference) {
            minDifference = difference;
            closestMassaStandard = massaStandard;
            closestMassaKonvensional = massaKonvensional;
          }
        }
      }
    }
    print(
      "The closest massa_standard: $closestMassaStandard g, massa_konvensional: $closestMassaKonvensional g",
    );

    return closestMassaKonvensional;
  }

  double _calculateRemainingNominal(int mainIndex, int currentIndex) {
    final nominalText = _linearityNominalControllers[mainIndex].text;
    final nominal = double.tryParse(nominalText) ?? 0.0;

    // Get the current linearity daya baca unit
    final dayaBacaUnit = _getSelectedLinearityDayaBacaUnit();

    // Convert nominal to grams since massa_standard is stored in grams
    final nominalInGrams = _convertToGrams(nominal, dayaBacaUnit);

    double usedMassa = 0.0;

    // Sum up all the massa_standard values from previous selections
    for (int i = 0; i < currentIndex; i++) {
      final anakTimbangUuid =
          _linearityConventionalControllers[mainIndex][i].text;
      if (anakTimbangUuid.isNotEmpty) {
        final massaKonvensional = _getLinearityMassaKonvensional(mainIndex, i);
        if (massaKonvensional != null) {
          // Find the corresponding massa_standard for this massa_konvensional
          final anakTimbang = _anakTimbangs.firstWhere(
            (at) => at['uuid'] == anakTimbangUuid,
            orElse: () => <String, dynamic>{},
          );

          if (anakTimbang.isNotEmpty) {
            try {
              final valueJson = anakTimbang['value'] as String?;
              if (valueJson != null && valueJson.isNotEmpty) {
                final List<dynamic> valueList = jsonDecode(valueJson);
                for (final item in valueList) {
                  if (item is Map<String, dynamic>) {
                    final itemMassaKonvensional = _parseMassaKonvensional(
                      item['massa_konvensional'],
                    );
                    if (itemMassaKonvensional == massaKonvensional) {
                      final massaStandardParsed = _parseMassaStandard(
                        item['massa_standard'],
                      );
                      final massaStandard =
                          massaStandardParsed['value'] as double?;
                      if (massaStandard != null) {
                        usedMassa += massaStandard;
                        break;
                      }
                    }
                  }
                }
              }
            } catch (e) {
              // Handle JSON parsing error
            }
          }
        }
      }
    }

    // Calculate remaining nominal in grams, then convert back to original unit
    final remainingInGrams = nominalInGrams - usedMassa;

    // Convert back to the original daya baca unit for consistency
    final remainingInOriginalUnit = _convertFromGrams(
      remainingInGrams,
      dayaBacaUnit,
    );

    print("Nominal: $nominal $dayaBacaUnit (${nominalInGrams} g)");
    print("Used massa: $usedMassa g");
    print(
      "Remaining: $remainingInOriginalUnit $dayaBacaUnit (${remainingInGrams} g)",
    );

    return remainingInOriginalUnit;
  }

  double? _getLinearityMassaKonvensional(int mainIndex, int subIndex) {
    final anakTimbangUuid =
        _linearityConventionalControllers[mainIndex][subIndex].text;
    if (anakTimbangUuid.isEmpty) return null;

    final anakTimbang = _anakTimbangs.firstWhere(
      (at) => at['uuid'] == anakTimbangUuid,
      orElse: () => <String, dynamic>{},
    );

    if (anakTimbang.isEmpty) return null;

    print(anakTimbang['value']);
    try {
      final valueJson = anakTimbang['value'] as String?;
      if (valueJson == null || valueJson.isEmpty) return null;

      final List<dynamic> valueList = jsonDecode(valueJson);
      final remainingNominal = _calculateRemainingNominal(mainIndex, subIndex);

      print("remaining $remainingNominal");

      return _findClosestMassaStandard(valueList, remainingNominal);
    } catch (e) {
      return null;
    }
  }

  String _getLinearityMassaKonvensionalDisplay(int mainIndex, int subIndex) {
    final massaKonvensional = _getLinearityMassaKonvensional(
      mainIndex,
      subIndex,
    );
    if (massaKonvensional == null) return '';

    // Check if the corresponding massa_standard has an asterisk
    bool hasAsterisk = false;
    try {
      final anakTimbangUuid =
          _linearityConventionalControllers[mainIndex][subIndex].text;
      if (anakTimbangUuid.isNotEmpty) {
        final anakTimbang = _anakTimbangs.firstWhere(
          (at) => at['uuid'] == anakTimbangUuid,
          orElse: () => <String, dynamic>{},
        );

        if (anakTimbang.isNotEmpty) {
          final valueJson = anakTimbang['value'] as String?;
          if (valueJson != null && valueJson.isNotEmpty) {
            final List<dynamic> valueList = jsonDecode(valueJson);
            for (final item in valueList) {
              if (item is Map<String, dynamic>) {
                final itemMassaKonvensional = _parseMassaKonvensional(
                  item['massa_konvensional'],
                );
                if (itemMassaKonvensional == massaKonvensional) {
                  final massaStandardParsed = _parseMassaStandard(
                    item['massa_standard'],
                  );
                  hasAsterisk = massaStandardParsed['hasAsterisk'] as bool;
                  break;
                }
              }
            }
          }
        }
      }
    } catch (e) {
      // Handle any errors silently
    }

    // Format with appropriate decimal places and add asterisk if needed
    final formattedValue = massaKonvensional.toString();
    return hasAsterisk ? '$formattedValue*' : formattedValue;
  }

  // Calculate total massa_konvensional for a linearity row
  double _getTotalMassaKonvensional(int mainIndex) {
    double total = 0.0;

    for (
      int i = 0;
      i < _linearityConventionalControllers[mainIndex].length;
      i++
    ) {
      final massaKonvensional = _getLinearityMassaKonvensional(mainIndex, i);
      if (massaKonvensional != null) {
        total += massaKonvensional;
      }
    }

    return total;
  }

  String _getTotalMassaKonvensionalDisplay(int mainIndex) {
    final total = _getTotalMassaKonvensional(mainIndex);
    if (total == 0.0) return '';
    return 'Total: ${total.toString()}';
  }

  // Check if there are multiple AT selections for a row
  bool _hasMultipleATSelections(int mainIndex) {
    int count = 0;
    for (
      int i = 0;
      i < _linearityConventionalControllers[mainIndex].length;
      i++
    ) {
      if (_linearityConventionalControllers[mainIndex][i].text.isNotEmpty) {
        count++;
      }
    }
    return count > 1;
  }

  // Helper methods for linearity calculations
  int _getLinearityDecimalDigits() {
    final dayaBacaValue = _getSelectedLinearityDayaBacaValue();
    if (dayaBacaValue.isEmpty) return 0;
    if (!dayaBacaValue.contains('.')) return 0;
    final decimalPart = dayaBacaValue.split('.')[1];
    return decimalPart.length;
  }

  String _getSelectedLinearityDayaBacaValue() {
    switch (_selectedLinearityDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  // Get the unit for the selected linearity daya baca
  String _getSelectedLinearityDayaBacaUnit() {
    switch (_selectedLinearityDayaBaca) {
      case '1':
        return _selectedDayaBacaR1Unit;
      case '2':
        return _selectedDayaBacaR2Unit;
      case '3':
        return _selectedDayaBacaR3Unit;
      default:
        return _selectedDayaBacaR1Unit;
    }
  }

  // Convert value to grams based on unit
  double _convertToGrams(double value, String unit) {
    switch (unit.toLowerCase()) {
      case 'kg':
        return value * 1000; // kg to g
      case 'mg':
        return value / 1000; // mg to g
      case 'g':
      default:
        return value; // already in grams
    }
  }

  // Convert value from grams to specified unit
  double _convertFromGrams(double valueInGrams, String unit) {
    switch (unit.toLowerCase()) {
      case 'kg':
        return valueInGrams / 1000; // g to kg
      case 'mg':
        return valueInGrams * 1000; // g to mg
      case 'g':
      default:
        return valueInGrams; // already in grams
    }
  }

  // Parse massa_standard value, handling asterisk (*) suffix
  Map<String, dynamic> _parseMassaStandard(dynamic massaStandardValue) {
    if (massaStandardValue == null) {
      return {'value': null, 'hasAsterisk': false};
    }

    if (massaStandardValue is num) {
      return {'value': massaStandardValue.toDouble(), 'hasAsterisk': false};
    }

    if (massaStandardValue is String) {
      String cleanValue = massaStandardValue.trim();
      bool hasAsterisk = cleanValue.endsWith('*');

      if (hasAsterisk) {
        cleanValue = cleanValue.substring(0, cleanValue.length - 1).trim();
      }

      // Handle comma as decimal separator
      cleanValue = cleanValue.replaceAll(',', '.');

      final parsedValue = double.tryParse(cleanValue);
      return {'value': parsedValue, 'hasAsterisk': hasAsterisk};
    }

    return {'value': null, 'hasAsterisk': false};
  }

  // Parse massa_konvensional value with high precision
  double? _parseMassaKonvensional(dynamic massaKonvensionalValue) {
    if (massaKonvensionalValue == null) {
      return null;
    }

    if (massaKonvensionalValue is num) {
      return massaKonvensionalValue.toDouble();
    }

    if (massaKonvensionalValue is String) {
      String cleanValue = massaKonvensionalValue.trim();

      // Handle comma as decimal separator
      cleanValue = cleanValue.replaceAll(',', '.');

      return double.tryParse(cleanValue);
    }

    return null;
  }

  String _formatLinearityRightYellowBox(int mainIndex, int readingIndex) {
    final nominalText = _linearityNominalControllers[mainIndex].text;
    final readingText =
        _linearityReadingControllers[mainIndex][readingIndex].text;

    // For first index (0), use 0 value; for second and third index (1,2), use filled nominal
    final baseValue = readingIndex == 0
        ? 0.0
        : (double.tryParse(nominalText) ?? 0.0);
    final readingValue = double.tryParse(readingText) ?? 0.0;

    final decimalDigits = _getLinearityDecimalDigits();
    final minimumDecimal = decimalDigits > 0
        ? (1.0 / math.pow(10, decimalDigits))
        : 0.1;

    final calculatedValue = baseValue + (readingValue * minimumDecimal);
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);

    return formattedValue.replaceAll('.', ',');
  }

  // Linearity pass/fail calculation methods
  double _calculateLinearityReadingResult(int mainIndex) {
    // Get yellow box values for second and third index
    final secondIndexValue = _parseRightYellowBoxValue(
      _formatLinearityRightYellowBox(mainIndex, 1),
    );
    final thirdIndexValue = _parseRightYellowBoxValue(
      _formatLinearityRightYellowBox(mainIndex, 2),
    );

    // Get first index value for current row
    final firstIndexValue = _parseRightYellowBoxValue(
      _formatLinearityRightYellowBox(mainIndex, 0),
    );

    // Get next row first index value (if exists)
    double nextRowFirstIndexValue = 0.0;
    if (mainIndex < 9) {
      // Not the last row
      nextRowFirstIndexValue = _parseRightYellowBoxValue(
        _formatLinearityRightYellowBox(mainIndex + 1, 0),
      );
    }

    // Formula: ((second + third) / 2) + ((first + next_row_first) / 2)
    final readingResult =
        ((secondIndexValue + thirdIndexValue) / 2) +
        ((firstIndexValue + nextRowFirstIndexValue) / 2);

    return readingResult;
  }

  double _calculateLinearityDeviasi(int mainIndex) {
    final totalMassaKonvensional = _getTotalMassaKonvensional(mainIndex);
    final readingResult = _calculateLinearityReadingResult(mainIndex);

    return totalMassaKonvensional - readingResult;
  }

  double _calculateLinearityMPE(int mainIndex) {
    final totalMassaKonvensional = _getTotalMassaKonvensional(mainIndex);
    final akurasi = double.tryParse(_accurationController.text) ?? 0.0;

    return totalMassaKonvensional * (akurasi / 100 / 2);
  }

  Map<String, dynamic> _calculateLinearityPassFail(int mainIndex) {
    final linearityDeviasi = _calculateLinearityDeviasi(mainIndex);
    final mpe = _calculateLinearityMPE(mainIndex);

    if (mpe == 0.0) {
      return {
        'result': 'FAIL',
        'isPass': false,
        'deviasi': linearityDeviasi,
        'mpe': mpe,
        'reason': 'MPE calculation error or missing data',
      };
    }

    final isPass = linearityDeviasi.abs() <= mpe;

    return {
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
      'deviasi': linearityDeviasi,
      'mpe': mpe,
      'readingResult': _calculateLinearityReadingResult(mainIndex),
      'totalMassaKonvensional': _getTotalMassaKonvensional(mainIndex),
      'reason': isPass
          ? 'Deviation is within MPE limit'
          : 'Deviation exceeds MPE limit',
    };
  }

  String _getLinearityPassFailText(int mainIndex) {
    final result = _calculateLinearityPassFail(mainIndex);
    return result['result'];
  }

  List<Color> _getLinearityPassFailColorGradient(int mainIndex) {
    final result = _calculateLinearityPassFail(mainIndex);
    return result['isPass']
        ? [Color.fromARGB(255, 0, 212, 71), Color.fromARGB(255, 63, 181, 85)]
        : [Color.fromARGB(255, 212, 0, 0), Color.fromARGB(255, 181, 63, 63)];
  }

  // Get overall linearity pass/fail result
  String _getLinearityOverallPassFail() {
    bool allPass = true;
    for (int i = 0; i < 10; i++) {
      final result = _calculateLinearityPassFail(i);
      if (!result['isPass']) {
        allPass = false;
        break;
      }
    }
    return allPass ? 'PASS' : 'FAIL';
  }

  // Calculate PASS/FAIL for Hysteresis
  Map<String, dynamic> _calculateHisterisysPassFail() {
    // Get M and M' nominal values
    final nominalM = double.tryParse(_histerisysMController.text) ?? 0.0;
    final nominalMPrime =
        double.tryParse(_histerisysMPrimeController.text) ?? 0.0;

    if (nominalM == 0.0 || nominalMPrime == 0.0) {
      return {
        'result': 'FAIL',
        'isPass': false,
        'hysteresisError': 0.0,
        'maxPermissibleHysteresis': 0.0,
        'reason': 'Missing nominal values for M or M\'',
      };
    }

    // Calculate hysteresis error from ascending and descending readings
    List<double> ascendingReadings = [];
    List<double> descendingReadings = [];

    // Parse ascending readings (first 8 values)
    for (int i = 0; i < 8; i++) {
      final value = double.tryParse(_histerisysValueControllers[i].text) ?? 0.0;
      ascendingReadings.add(value);
    }

    // Parse descending readings (last 8 values)
    for (int i = 8; i < 16; i++) {
      final value = double.tryParse(_histerisysValueControllers[i].text) ?? 0.0;
      descendingReadings.add(value);
    }

    // Calculate maximum difference between corresponding readings
    double maxHysteresisError = 0.0;
    for (
      int i = 0;
      i < ascendingReadings.length && i < descendingReadings.length;
      i++
    ) {
      final difference = (ascendingReadings[i] - descendingReadings[i]).abs();
      if (difference > maxHysteresisError) {
        maxHysteresisError = difference;
      }
    }

    // Calculate maximum permissible hysteresis (typically 1.5 × accuracy)
    final accuracy = double.tryParse(_accurationController.text) ?? 0.1;
    final maxPermissibleHysteresis =
        (nominalM + nominalMPrime) * (accuracy / 100) * 1.5;

    // Determine PASS/FAIL
    final isPass = maxHysteresisError <= maxPermissibleHysteresis;

    return {
      'result': isPass ? 'PASS' : 'FAIL',
      'isPass': isPass,
      'hysteresisError': maxHysteresisError,
      'maxPermissibleHysteresis': maxPermissibleHysteresis,
      'ascendingReadings': ascendingReadings,
      'descendingReadings': descendingReadings,
      'reason': isPass
          ? 'Hysteresis error is within acceptable limits'
          : 'Hysteresis error exceeds maximum permissible limit',
    };
  }

  // Get PASS/FAIL result text for Hysteresis
  String _getHisterisysPassFailText() {
    final result = _calculateHisterisysPassFail();
    return result['result'];
  }

  // Get PASS/FAIL color for Hysteresis
  List<Color> _getHisterisysPassFailColorGradient() {
    final result = _calculateHisterisysPassFail();
    return result['isPass']
        ? [Color.fromARGB(255, 0, 212, 71), Color.fromARGB(255, 63, 181, 85)]
        : [Color.fromARGB(255, 212, 0, 0), Color.fromARGB(255, 181, 63, 63)];
  }

  // Helper methods for histerisys calculations
  int _getHisterisysDecimalDigits() {
    final dayaBacaValue = _getSelectedHisterisysDayaBacaValue();
    if (dayaBacaValue.isEmpty) return 0;
    if (!dayaBacaValue.contains('.')) return 0;
    final decimalPart = dayaBacaValue.split('.')[1];
    return decimalPart.length;
  }

  String _getSelectedHisterisysDayaBacaValue() {
    switch (_selectedHisterisysDayaBaca) {
      case '1':
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
      case '2':
        return _isManualInputR2
            ? _dayaBacaR2ValueController.text
            : _selectedDayaBacaR2Value;
      case '3':
        return _isManualInputR3
            ? _dayaBacaR3ValueController.text
            : _selectedDayaBacaR3Value;
      default:
        return _isManualInputR1
            ? _dayaBacaR1ValueController.text
            : _selectedDayaBacaR1Value;
    }
  }

  String _formatHisterisysLeftYellowBox(int index) {
    final label = histerisys[index]['label'] ?? '';
    final decimalDigits = _getHisterisysDecimalDigits();

    if (label == 'M+M\'') {
      // For M+M', use nominal M + nominal M'
      final nominalM = double.tryParse(_histerisysMController.text) ?? 0.0;
      final nominalMPrime =
          double.tryParse(_histerisysMPrimeController.text) ?? 0.0;
      final calculatedValue = nominalM + nominalMPrime;
      final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);
      return formattedValue.replaceAll('.', ',');
    } else if (label == 'Zero') {
      // For Zero, always show 0
      final formattedValue = 0.0.toStringAsFixed(decimalDigits);
      return formattedValue.replaceAll('.', ',');
    } else {
      // For M(p1), M(q1), etc., use nominal M value
      final nominalValue = double.tryParse(_histerisysMController.text) ?? 0.0;
      final formattedValue = nominalValue.toStringAsFixed(decimalDigits);
      return formattedValue.replaceAll('.', ',');
    }
  }

  String _formatHisterisysRightYellowBox(int index) {
    final middleValueText = _histerisysValueControllers[index].text;

    if (middleValueText.isEmpty) {
      return _formatHisterisysLeftYellowBox(index);
    }

    final label = histerisys[index]['label'] ?? '';
    final middleValue = double.tryParse(middleValueText) ?? 0.0;
    final decimalDigits = _getHisterisysDecimalDigits();
    final minimumDecimal =
        1.0 / (decimalDigits > 0 ? math.pow(10, decimalDigits) : 10);

    double baseValue;
    if (label == 'M+M\'') {
      // For M+M', use nominal M + nominal M'
      final nominalM = double.tryParse(_histerisysMController.text) ?? 0.0;
      final nominalMPrime =
          double.tryParse(_histerisysMPrimeController.text) ?? 0.0;
      baseValue = nominalM + nominalMPrime;
    } else if (label == 'Zero') {
      baseValue = 0.0;
    } else {
      baseValue = double.tryParse(_histerisysMController.text) ?? 0.0;
    }

    final calculatedValue = baseValue + (middleValue * minimumDecimal);
    final formattedValue = calculatedValue.toStringAsFixed(decimalDigits);
    return formattedValue.replaceAll('.', ',');
  }

  // Show eccentricity daya baca selection dialog
  void _showEccentricityDayaBacaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Daya Baca - Eccentricity'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Column(
              children: [
                ListTile(
                  title: const Text('Daya Baca 1'),
                  trailing: _selectedEccentricityDayaBaca == '1'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedEccentricityDayaBaca = '1';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 2'),
                  trailing: _selectedEccentricityDayaBaca == '2'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedEccentricityDayaBaca = '2';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 3'),
                  trailing: _selectedEccentricityDayaBaca == '3'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedEccentricityDayaBaca = '3';
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  // Show histerisys daya baca selection dialog
  void _showHisterisysDayaBacaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Daya Baca - Histerisys'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Column(
              children: [
                ListTile(
                  title: const Text('Daya Baca 1'),
                  trailing: _selectedHisterisysDayaBaca == '1'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedHisterisysDayaBaca = '1';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 2'),
                  trailing: _selectedHisterisysDayaBaca == '2'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedHisterisysDayaBaca = '2';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 3'),
                  trailing: _selectedHisterisysDayaBaca == '3'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedHisterisysDayaBaca = '3';
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  // Show linearity daya baca selection dialog
  void _showLinearityDayaBacaDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Daya Baca - Linearity'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Column(
              children: [
                ListTile(
                  title: const Text('Daya Baca 1'),
                  trailing: _selectedLinearityDayaBaca == '1'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedLinearityDayaBaca = '1';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 2'),
                  trailing: _selectedLinearityDayaBaca == '2'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedLinearityDayaBaca = '2';
                    });
                  },
                ),
                ListTile(
                  title: const Text('Daya Baca 3'),
                  trailing: _selectedLinearityDayaBaca == '3'
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedLinearityDayaBaca = '3';
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  // Responsive helper methods
  bool get isMobile {
    try {
      return MediaQuery.of(context).size.width < 768;
    } catch (e) {
      return false; // Default to desktop layout if context is not available
    }
  }

  bool get isTablet {
    try {
      return MediaQuery.of(context).size.width >= 768 &&
          MediaQuery.of(context).size.width < 1024;
    } catch (e) {
      return false;
    }
  }

  bool get isDesktop {
    try {
      return MediaQuery.of(context).size.width >= 1024;
    } catch (e) {
      return true; // Default to desktop layout
    }
  }

  double get screenWidth {
    try {
      return MediaQuery.of(context).size.width;
    } catch (e) {
      return 1024.0; // Default width
    }
  }

  double get screenHeight {
    try {
      return MediaQuery.of(context).size.height;
    } catch (e) {
      return 768.0; // Default height
    }
  }

  // Responsive widths
  double get nominalFieldWidth =>
      isMobile ? screenWidth * 0.9 : (isTablet ? 300.0 : 380.0);
  double get yellowContainerWidth => 120.0; // Keep original size
  double get cardSpacing => isMobile ? 8.0 : 16.0;
  double get sectionSpacing => isMobile ? 16.0 : 26.0;

  // Responsive padding
  EdgeInsets get screenPadding => EdgeInsets.all(isMobile ? 8.0 : 16.0);
  EdgeInsets get cardPadding => EdgeInsets.all(isMobile ? 8.0 : 16.0);

  // Helper method to wrap widgets responsively
  Widget _responsiveCard(Widget card) {
    return isMobile ? card : Expanded(child: card);
  }

  // Build new timbangan input form
  Widget _buildNewTimbanganForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ID Timbangan
        const Text('ID Timbangan'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganIdController,
          placeholder: const Text('ID Timbangan'),
        ),
        const SizedBox(height: 16),

        // Nama Alat (Timbangan Type)
        const Text('Nama Alat'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganTypeController,
          placeholder: const Text('Nama Alat'),
        ),
        const SizedBox(height: 16),

        // Merk (Brand)
        const Text('Merk'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganBrandController,
          placeholder: const Text('Merk'),
        ),
        const SizedBox(height: 16),

        // Type (Model)
        const Text('Type'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganModelController,
          placeholder: const Text('Type'),
        ),
        const SizedBox(height: 16),

        // SNR
        const Text('SNR'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganSNRController,
          placeholder: const Text('SNR'),
        ),
        const SizedBox(height: 16),

        // Kapasitas
        const Text('Kapasitas'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganCapacityController,
          placeholder: const Text('Kapasitas'),
        ),
        const SizedBox(height: 16),

        // Lokasi
        const Text('Lokasi'),
        const SizedBox(height: 6),
        ShadInput(
          controller: _newTimbanganLocationController,
          placeholder: const Text('Lokasi'),
        ),
      ],
    );
  }

  // Build daya baca input with switch toggle
  Widget _buildDayaBacaInput({
    required String title,
    required String selectedValue,
    required bool isManualInput,
    required TextEditingController manualController,
    required Function(String) onDropdownChanged,
    required Function(bool) onManualInputChanged,
    required String unit,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title),
            Row(
              children: [
                Text(
                  isManualInput ? 'Manual' : 'Selection',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(width: 8),
                // Switch(
                //   value: isManualInput,
                //   onChanged: onManualInputChanged,
                //   activeColor: Colors.blue,
                // ),
                ShadSwitchFormField(
                  id: 'terms',
                  initialValue: false,
                  checkedTrackColor: isManualInput
                      ? const Color.fromARGB(255, 44, 53, 60)
                      : Colors.grey,
                  // inputLabel: const Text('I accept the terms and conditions'),
                  onChanged: onManualInputChanged,
                  // inputSublabel: const Text(
                  // 'You agree to our Terms and Conditions',
                  // ),
                  validator: (v) {
                    if (!v) {
                      return 'You must accept the terms and conditions';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 6),
        Row(
          children: [
            if (isManualInput)
              SizedBox(
                width: 200.0,
                child: ShadInput(
                  controller: manualController,
                  placeholder: const Text('Input Daya Baca'),
                  trailing: Text(unit),
                  keyboardType: const TextInputType.numberWithOptions(
                    signed: true,
                    decimal: true,
                  ),
                ),
              )
            else
              SizedBox(
                width: 200.0,
                child: ShadSelect<String>(
                  placeholder: const Text('Pilih Daya Baca'),
                  options: [
                    const ShadOption(value: '1', child: Text('1')),
                    const ShadOption(value: '0.1', child: Text('0.1')),
                    const ShadOption(value: '0.001', child: Text('0.001')),
                    const ShadOption(value: '0.0001', child: Text('0.0001')),
                    const ShadOption(value: '0.00001', child: Text('0.00001')),
                    const ShadOption(
                      value: '0.000001',
                      child: Text('0.000001'),
                    ),
                    const ShadOption(
                      value: '0.0000001',
                      child: Text('0.0000001'),
                    ),
                  ],
                  selectedOptionBuilder: (context, value) => Text(value),
                  onChanged: (value) {
                    if (value != null) {
                      onDropdownChanged(value);
                    }
                  },
                  initialValue: selectedValue,
                ),
              ),
            if (title == 'Daya Baca')
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.height * 0.12,
                ),
                child: ShadSelect<String>(
                  placeholder: Text(_selectedDayaBacaR1Unit),
                  options: _dayaBacaUnits
                      .map((unit) => ShadOption(value: unit, child: Text(unit)))
                      .toList(),
                  selectedOptionBuilder: (context, value) {
                    return Text(value);
                  },
                  onChanged: (value) {
                    if (value != null) {
                      _onR1UnitChanged(value);
                    }
                  },
                ),
              ),
          ],
        ),
      ],
    );
  }

  // Save new timbangan to database and return UUID
  Future<String> _saveNewTimbangan() async {
    final newUuid = const Uuid().v4();

    final timbanganData = {
      'uuid': newUuid,
      'id_timbangan': _newTimbanganIdController.text.trim(),
      'timbangan_type': _newTimbanganTypeController.text.trim(),
      'brand': _newTimbanganBrandController.text.trim(),
      'model': _newTimbanganModelController.text.trim(),
      'snr': _newTimbanganSNRController.text.trim(),
      'capacity': _newTimbanganCapacityController.text.trim(),
      'location': _newTimbanganLocationController.text.trim(),
      'branch_id': _selectedBranchId, // Associate with current branch
    };

    try {
      final db = await widget.repository.dbHelper.database;
      await db.insert('master_timbangan', timbanganData);
      print('New timbangan saved with UUID: $newUuid');
      return newUuid;
    } catch (e) {
      print('Error saving new timbangan: $e');
      throw Exception('Failed to save new timbangan: $e');
    }
  }

  @override
  void dispose() {
    // Dispose new timbangan controllers
    _newTimbanganIdController.dispose();
    _newTimbanganTypeController.dispose();
    _newTimbanganBrandController.dispose();
    _newTimbanganModelController.dispose();
    _newTimbanganSNRController.dispose();
    _newTimbanganCapacityController.dispose();
    _newTimbanganLocationController.dispose();
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _contactPersonController.dispose();
    _cpDivisionController.dispose();
    _certNumberController.dispose();

    // Dispose additional controllers
    _timbanganIdController.dispose();

    _orderNumberController.dispose();
    _dayaBacaR1ValueController.dispose();
    _dayaBacaR2ValueController.dispose();
    _dayaBacaR3ValueController.dispose();
    _accurationController.dispose();
    _beforeTempController.dispose();
    _beforeHumidityController.dispose();
    _beforeBarrometerController.dispose();

    for (var controller in _weightSetControllers) {
      controller.dispose();
    }

    _preAdjustmentNominalController.dispose();
    for (var controller in _preAdjustmentValueControllers) {
      controller.dispose();
    }
    _preAdjustmentIdController.dispose();

    _repeatHalfNominalController.dispose();
    for (var controller in _repeatHalfValueControllers1) {
      controller.dispose();
    }
    for (var controller in _repeatHalfValueControllers2) {
      controller.dispose();
    }

    _repeatFullNominalController.dispose();
    for (var controller in _repeatFullValueControllers1) {
      controller.dispose();
    }
    for (var controller in _repeatFullValueControllers2) {
      controller.dispose();
    }

    // Dispose additional capacity cards
    for (var card in _additionalCapacityCards) {
      (card['nominalController'] as TextEditingController).dispose();
      for (var controller
          in card['valueControllers1'] as List<TextEditingController>) {
        controller.dispose();
      }
      for (var controller
          in card['valueControllers2'] as List<TextEditingController>) {
        controller.dispose();
      }
    }

    _linearityUnitController.dispose();
    for (var controller in _linearityNominalControllers) {
      controller.dispose();
    }
    for (var list in _linearityConventionalControllers) {
      for (var controller in list) {
        controller.dispose();
      }
    }
    for (var list in _linearityReadingControllers) {
      for (var controller in list) {
        controller.dispose();
      }
    }

    _eccentricityNominalController.dispose();
    for (var controller in _eccentricityValueControllers) {
      controller.dispose();
    }

    _histerisysMController.dispose();
    _histerisysMPrimeController.dispose();
    for (var controller in _histerisysValueControllers) {
      controller.dispose();
    }

    _afterTempController.dispose();
    _afterHumidityController.dispose();
    _afterBarrometerController.dispose();

    super.dispose();
  }

  // Trigger calibration sync in background
  void _triggerCalibrationSync() async {
    try {
      final syncService = CalibrationSyncService(
        dbHelper: widget.repository.dbHelper,
        apiBaseUrl: widget.repository.apiBaseUrl,
      );

      if (await syncService.shouldSync()) {
        final result = await syncService.syncCalibrationRequests();
        print('Calibration sync result: ${result.message}');
        print(
          'Uploaded: ${result.uploadedCount}, Downloaded: ${result.downloadedCount}',
        );
      }
    } catch (e) {
      print('Background calibration sync failed: $e');
    }
  }

  // Trigger timbangan sync in background
  void _triggerTimbanganSync() async {
    try {
      final syncService = MasterDataSyncService(
        dbHelper: widget.repository.dbHelper,
        apiBaseUrl: widget.repository.apiBaseUrl,
      );

      if (await syncService.shouldSync()) {
        await syncService.syncTimbangan();
      }
    } catch (e) {
      print('Background timbangan sync failed: $e');
    }
  }

  final frameworks = {
    'next': 'Next.js',
    'react': 'React',
    'astro': 'Astro',
    'nuxt': 'Nuxt.js',
  };

  final branches = {
    '00': 'Bandung',
    '01': 'Jakarta',
    '02': 'Semarang',
    '03': 'Surabaya',
    '04': 'Lab_Kalibrasi',
  };

  // final anakTimbang = {
  //   'AT.00.02': 'AT.00.02',
  //   'AT.00.03': 'AT.00.03',
  //   'AT.00.04': 'AT.00.04',
  //   'AT.00.05': 'AT.00.05',
  // };

  final eccentricity = [
    {'label': 'C'},
    {'label': 'F'},
    {'label': 'B'},
    {'label': 'R'},
    {'label': 'L'},
  ];

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('Add New Request')),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.all(16.0),
          children: [
            Text('Branch', style: theme.textTheme.h4),
            const SizedBox(height: 6),
            _buildSearchableDropdown(
              title: 'Branch',
              selectedValue: _selectedBranch.isEmpty ? null : _selectedBranch,
              placeholder: 'Select Branch',
              items: _branches,
              valueKey: 'uuid',
              displayKey: 'branch_name',
              searchKeys: ['branch_name', 'branch_code'],
              subtitleKey: 'branch_code',
              enabled:
                  false, // Disabled because it's prefilled from logged-in user
              onSelected: (value) {
                setState(() {
                  _selectedBranch = value;
                });
              },
            ),
            const SizedBox(height: 16.0),

            // Customer Information Section
            ShadCard(
              title: Text('Customer Information', style: theme.textTheme.h4),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // SOP Warning Message
                    WarningAlert(
                      title: 'Catatan:',
                      text:
                          '1. Lakukan pemanasan Timbangan min 30 menit atau sesuai IK\n'
                          '2. Gunakan Anak Timbang sesuai IK\n'
                          '3. Pastikan Anak Timbang Terkalibrasi\n'
                          '4. Gunakan sarung tangan dan alat bantu untuk penanganan anak timbang\n'
                          '5. Pastikan pengisian Data sesuai',
                    ),
                    const Text('Order Number'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _orderNumberController,
                      placeholder: const Text('Enter order number'),
                    ),
                    const SizedBox(height: 16),
                    const Text('Company Name'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _companyNameController,
                      placeholder: const Text('Enter company name'),
                    ),
                    const SizedBox(height: 16),
                    const Text('Company Address'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _companyAddressController,
                      placeholder: const Text('Enter company address'),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    const Text('Contact Person'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _contactPersonController,
                      placeholder: const Text('Enter contact person name'),
                    ),
                    const SizedBox(height: 16),
                    const Text('Contact Person Division'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _cpDivisionController,
                      placeholder: const Text('Enter division/department'),
                    ),
                    const SizedBox(height: 16),
                    const Text('Certificate Atas Nama'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _certNumberController,
                      placeholder: const Text('Enter Name'),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16.0),
            Flex(
              direction: isMobile ? Axis.vertical : Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: ShadCard(
                    // width: 350,
                    title: Text('Data Timbangan', style: theme.textTheme.h4),

                    // description: const Text('Deploy your new project in one-click.'),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const Text('Tanggal Kalibrasi'),
                          const SizedBox(height: 6),
                          ShadDatePicker(
                            selected: _selectedCalibrationDate,
                            onChanged: (date) {
                              setState(() {
                                _selectedCalibrationDate = date;
                              });
                            },
                            placeholder: const Text('Pilih Tanggal Kalibrasi'),
                          ),
                          const SizedBox(height: 16),
                          const Text('Inisial Teknisi'),
                          const SizedBox(height: 6),
                          _buildSearchableDropdown(
                            title: 'Teknisi',
                            selectedValue: _selectedTechnician.isEmpty
                                ? null
                                : _selectedTechnician,
                            placeholder: 'Pilih Teknisi',
                            items: _technicians,
                            valueKey: 'uuid',
                            displayKey: 'tech_name',
                            searchKeys: ['tech_name', 'tech_initial'],
                            subtitleKey: 'tech_initial',
                            enabled:
                                false, // Disabled because it's prefilled from logged-in user
                            onSelected: (value) {
                              setState(() {
                                _selectedTechnician = value;
                              });
                            },
                          ),

                          const SizedBox(height: 16),
                          _buildDayaBacaInput(
                            title: 'Daya Baca',
                            selectedValue: _selectedDayaBacaR1Value,
                            isManualInput: _isManualInputR1,
                            manualController: _dayaBacaR1ValueController,
                            onDropdownChanged: (value) {
                              setState(() {
                                _selectedDayaBacaR1Value = value;
                              });
                            },
                            onManualInputChanged: (isManual) {
                              setState(() {
                                _isManualInputR1 = isManual;
                              });
                            },
                            unit: _selectedDayaBacaR1Unit,
                          ),

                          const SizedBox(height: 16),
                          _buildDayaBacaInput(
                            title: 'Daya Baca 2',
                            selectedValue: _selectedDayaBacaR2Value,
                            isManualInput: _isManualInputR2,
                            manualController: _dayaBacaR2ValueController,
                            onDropdownChanged: (value) {
                              setState(() {
                                _selectedDayaBacaR2Value = value;
                              });
                            },
                            onManualInputChanged: (isManual) {
                              setState(() {
                                _isManualInputR2 = isManual;
                              });
                            },
                            unit: _selectedDayaBacaR2Unit,
                          ),
                          const SizedBox(height: 16),
                          _buildDayaBacaInput(
                            title: 'Daya Baca 3',
                            selectedValue: _selectedDayaBacaR3Value,
                            isManualInput: _isManualInputR3,
                            manualController: _dayaBacaR3ValueController,
                            onDropdownChanged: (value) {
                              setState(() {
                                _selectedDayaBacaR3Value = value;
                              });
                            },
                            onManualInputChanged: (isManual) {
                              setState(() {
                                _isManualInputR3 = isManual;
                              });
                            },
                            unit: _selectedDayaBacaR3Unit,
                          ),
                          const SizedBox(height: 16),
                          const Text('Akurasi'),
                          const SizedBox(height: 6),
                          SizedBox(
                            width: 380.0,
                            child: ShadInput(
                              controller: _accurationController,
                              placeholder: const Text('Akurasi'),
                              trailing: Text('%'),
                            ),
                          ),
                          const SizedBox(height: 16),
                          _buildTimbanganInput(),
                          const SizedBox(height: 16),
                          if (!_isAddNewTimbangan) ...[
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "Nama Alat",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Flexible(
                                  child: Text(
                                    _namaAlat,
                                    style: theme.textTheme.muted,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "Merk",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Text(_merk, style: theme.textTheme.muted),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "Type",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Text(_type, style: theme.textTheme.muted),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "SNR",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Flexible(
                                  child: Text(
                                    _snr,
                                    style: theme.textTheme.muted,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "Kap",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Text(_kapasitas, style: theme.textTheme.muted),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minWidth:
                                        MediaQuery.of(context).size.height *
                                        0.3,
                                  ),
                                  child: Text(
                                    "Lokasi",
                                    style: theme.textTheme.small,
                                  ),
                                ),
                                SizedBox(width: 8.0),
                                Flexible(
                                  child: Text(
                                    _lokasi,
                                    style: theme.textTheme.muted,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 26.0),
                Flexible(
                  child: ShadCard(
                    // width: 350,
                    title: Text('Before', style: theme.textTheme.h4),

                    // description: const Text('Deploy your new project in one-click.'),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const Text('Temperature'),
                          const SizedBox(height: 6),
                          ShadInput(
                            controller: _beforeTempController,
                            placeholder: const Text('Temperature'),
                            trailing: const Text("˚c"),
                          ),
                          const SizedBox(height: 16),
                          const Text('Humidity'),
                          const SizedBox(height: 6),
                          ShadInput(
                            controller: _beforeHumidityController,
                            placeholder: const Text('Humidity'),
                            trailing: const Text("˚%"),
                          ),
                          const SizedBox(height: 16),
                          const Text('Barrometer'),
                          const SizedBox(height: 6),
                          ShadInput(
                            controller: _beforeBarrometerController,
                            placeholder: const Text('Barrometer'),
                            trailing: const Text("kpa"),
                          ),
                          const SizedBox(height: 16),
                          const Text('Standard'),
                          const SizedBox(height: 6),
                          _buildSearchableDropdown(
                            title: 'Standard',
                            selectedValue: _selectedStandard.isEmpty
                                ? null
                                : _selectedStandard,
                            placeholder: 'Pilih Standard',
                            items: _standards,
                            valueKey: 'uuid',
                            displayKey: 'no_invent',
                            searchKeys: ['no_invent', 'brand', 'model'],
                            subtitleKey: 'brand',
                            onSelected: (value) {
                              setState(() {
                                _selectedStandard = value;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          const Text('Weight Set / Anak Timbang'),
                          const SizedBox(height: 6),
                          ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 8, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child:
                                          _buildSearchableAnakTimbangDropdown(
                                            index,
                                          ),
                                    ),
                                    Container(
                                      width: 100.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        border: Border(
                                          right: BorderSide(
                                            color: const Color.fromARGB(
                                              255,
                                              195,
                                              195,
                                              195,
                                            ),
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _getWeightSetRangeCapacity(index),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 80.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(_getWeightSetClass(index)),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ShadCard(
              // width: 350,
              title: Text('Pre-adjustment', style: theme.textTheme.h4),

              // description: const Text('Deploy your new project in one-click.'),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WarningAlert(
                      title: 'Catatan:',
                      text:
                          'Gunakan Anak Timbang mendekati Kap Max / pada kapasitas Adjustmennya.',
                    ),
                    const Text('Nominal'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 380.0,
                      child: ShadInput(
                        controller: _preAdjustmentNominalController,
                        placeholder: Text('Nominal'),
                        trailing: Text(_selectedDayaBacaR1Unit),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Daya Baca Selection
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: _buildSearchableDropdown(
                        title: 'Daya Baca',
                        selectedValue: _selectedPreAdjustmentDayaBaca,
                        placeholder: 'Pilih Daya Baca',
                        items: [
                          {'value': '1', 'label': 'Daya Baca 1'},
                          {'value': '2', 'label': 'Daya Baca 2'},
                          {'value': '3', 'label': 'Daya Baca 3'},
                        ],
                        valueKey: 'value',
                        displayKey: 'label',
                        onSelected: (value) {
                          setState(() {
                            _selectedPreAdjustmentDayaBaca = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: 200.0,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Anak Timbang :'),
                          const SizedBox(height: 6),
                          _buildSearchablePreAdjustmentAnakTimbangDropdown(),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Column(
                      children: [
                        Container(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Row(
                            children: [
                              SizedBox(width: 10.0),
                              Container(
                                padding: EdgeInsets.all(8.0),
                                width: 110.0,
                                child: Text("Nominal"),
                              ),
                              SizedBox(width: 10.0),
                              Expanded(
                                child: Container(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text("Correction"),
                                ),
                              ),
                              SizedBox(width: 10.0),
                              Container(
                                padding: EdgeInsets.all(8.0),
                                width: 160.0,
                                child: Text("Reading"),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          height: 1.0,
                          color: const Color.fromARGB(255, 171, 169, 169),
                        ),
                        SizedBox(height: 8),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 4, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatYellowBoxValue(index),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _preAdjustmentValueControllers[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 40.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        // borderRadius: BorderRadius.only(
                                        //   topRight: Radius.circular(10.0),
                                        //   bottomRight: Radius.circular(10.0),
                                        // ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          preadjustmentZM[index]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRightYellowBoxValue(index),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16.0),

            ShadCard(
              // width: 350,
              title: Text('REPEAT Half Capacity', style: theme.textTheme.h4),

              // description: const Text('Deploy your new project in one-click.'),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.warning_amber_rounded,
                                color: Colors.orange.shade700,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Catatan:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '1. Gunakan Anak Timbang mendekati ½ kap Max  dan kap Max\n'
                            '2. Gunakan 1 anak Timbang atau lebih',
                            style: TextStyle(
                              color: Colors.orange.shade800,
                              fontSize: 12,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Text('Nominal'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 380.0,
                      child: ShadInput(
                        controller: _repeatHalfNominalController,
                        placeholder: Text('Nominal'),
                        trailing: Text(_selectedDayaBacaR1Unit),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Daya Baca Selection for Repeat Half Capacity
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: _buildSearchableDropdown(
                        title: 'Daya Baca',
                        selectedValue: _selectedRepeatHalfDayaBaca,
                        placeholder: 'Pilih Daya Baca',
                        items: [
                          {'value': '1', 'label': 'Daya Baca 1'},
                          {'value': '2', 'label': 'Daya Baca 2'},
                          {'value': '3', 'label': 'Daya Baca 3'},
                        ],
                        valueKey: 'value',
                        displayKey: 'label',
                        onSelected: (value) {
                          setState(() {
                            _selectedRepeatHalfDayaBaca = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.only(bottom: 4.0),
                      child: Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 120.0,
                            child: Text("Nominal"),
                          ),
                          // SizedBox(width: 10.0),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.all(8.0),
                              child: Text("Correction"),
                            ),
                          ),
                          // SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 160.0,
                            child: Text("Reading"),
                          ),
                          const SizedBox(width: 16.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 120.0,
                            child: Text("Nominal"),
                          ),
                          // SizedBox(width: 10.0),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.all(8.0),
                              child: Text("Correction"),
                            ),
                          ),
                          // SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 160.0,
                            child: Text("Reading"),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 10, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatHalfLeftYellowBoxValue(
                                            index,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _repeatHalfValueControllers1[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 40.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                      ),
                                      child: Center(
                                        child: Text(
                                          repeatCapacityZ[index]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatHalfRightYellowBoxValue(
                                            index,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16.0),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatHalfLeftYellowBoxValue(
                                            index + 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _repeatHalfValueControllers2[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 40.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                      ),
                                      child: Center(
                                        child: Text(
                                          repeatCapacityM[index]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatHalfRightYellowBoxValue(
                                            index + 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16.0),
                    Column(
                      children: [
                        ShadButton(
                          size: ShadButtonSize.lg,
                          onPressed: () {},
                          gradient: LinearGradient(
                            colors: _getRepeatHalfPassFailColorGradient(),
                          ),

                          child: Text(
                            _getRepeatHalfPassFailText(),
                            style: TextStyle(
                              // color: _getRepeatHalfPassFailColor(),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 60.0),

                    Text('REPEAT Full Capacity', style: theme.textTheme.h4),
                    const SizedBox(height: 16),
                    const Text('Nominal'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 380.0,
                      child: ShadInput(
                        controller: _repeatFullNominalController,
                        placeholder: Text('Nominal'),
                        trailing: Text(_selectedDayaBacaR1Unit),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Daya Baca Selection for Repeat Full Capacity
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: _buildSearchableDropdown(
                        title: 'Daya Baca',
                        selectedValue: _selectedRepeatFullDayaBaca,
                        placeholder: 'Pilih Daya Baca',
                        items: [
                          {'value': '1', 'label': 'Daya Baca 1'},
                          {'value': '2', 'label': 'Daya Baca 2'},
                          {'value': '3', 'label': 'Daya Baca 3'},
                        ],
                        valueKey: 'value',
                        displayKey: 'label',
                        onSelected: (value) {
                          setState(() {
                            _selectedRepeatFullDayaBaca = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Header row for repeat full capacity
                    Container(
                      padding: EdgeInsets.only(bottom: 4.0),
                      child: Row(
                        children: [
                          SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 120.0,
                            child: Text("Nominal"),
                          ),
                          // SizedBox(width: 10.0),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.all(8.0),
                              child: Text("Correction"),
                            ),
                          ),
                          // SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 160.0,
                            child: Text("Reading"),
                          ),
                          const SizedBox(width: 16.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 120.0,
                            child: Text("Nominal"),
                          ),
                          // SizedBox(width: 10.0),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.all(8.0),
                              child: Text("Correction"),
                            ),
                          ),
                          // SizedBox(width: 10.0),
                          Container(
                            padding: EdgeInsets.all(8.0),
                            width: 160.0,
                            child: Text("Reading"),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 10, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatFullLeftYellowBoxValue(
                                            index,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _repeatFullValueControllers1[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 40.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                      ),
                                      child: Center(
                                        child: Text(
                                          repeatCapacityZ[index]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatFullRightYellowBoxValue(
                                            index,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16.0),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatFullLeftYellowBoxValue(
                                            index + 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _repeatFullValueControllers2[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 40.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                      ),
                                      child: Center(
                                        child: Text(
                                          repeatCapacityM[index]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatRepeatFullRightYellowBoxValue(
                                            index + 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16.0),
                    Column(
                      children: [
                        ShadButton(
                          size: ShadButtonSize.lg,
                          onPressed: () {},
                          gradient: LinearGradient(
                            colors: _getRepeatFullPassFailColorGradient(),
                          ),

                          child: Text(
                            _getRepeatFullPassFailText(),
                            style: TextStyle(
                              // color: _getRepeatFullPassFailColor(),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 60),
                    ShadButton(
                      onPressed: _addCapacityCard,
                      leading: const Icon(LucideIcons.plus),
                      child: const Text('Add Capacity'),
                    ),
                  ],
                ),
              ),
            ),

            // Additional Capacity Cards
            ..._additionalCapacityCards.asMap().entries.map((entry) {
              final index = entry.key;
              final cardData = entry.value;
              return Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: _buildAdditionalCapacityCard(cardData, index),
              );
            }),

            const SizedBox(height: 16.0),

            // --PAGE LINEARITY
            ShadCard(
              // width: 350,
              title: Text('Linearity', style: theme.textTheme.h4),

              // description: const Text('Deploy your new project in one-click.'),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WarningAlert(
                      title: 'Catatan:',
                      text:
                          '1. Pilih 10 titik uji dengan pembagian teratur atau atas permintaan pelanggan\n'
                          '2. Untuk timbangan multi range lakukan pengujian minimal 5 titik',
                    ),
                    Text('Satuan : $_selectedDayaBacaR1Unit'),
                    const SizedBox(height: 6),

                    // Daya Baca Selection for Linearity
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: GestureDetector(
                        onTap: () => _showLinearityDayaBacaDialog(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Daya Baca $_selectedLinearityDayaBaca',
                                  style: const TextStyle(color: Colors.black),
                                ),
                              ),
                              const Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Column(
                      children: [
                        Container(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Row(
                            children: [
                              SizedBox(width: 20.0),
                              Container(
                                padding: EdgeInsets.all(8.0),
                                width: 80.0,
                                child: Text("Nominal"),
                              ),
                              SizedBox(width: 10.0),
                              Container(
                                padding: EdgeInsets.all(8.0),
                                width: 210.0,
                                child: Text("Konvensional"),
                              ),
                              SizedBox(width: 10.0),
                              SizedBox(width: 200.0, child: Text("Reading")),
                            ],
                          ),
                        ),
                        Divider(
                          height: 1.0,
                          color: const Color.fromARGB(255, 171, 169, 169),
                        ),
                        SizedBox(height: 8),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          flex: 5,
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 10, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        SizedBox(
                                          width: 20.0,
                                          child: Text((index + 1).toString()),
                                        ),
                                        SizedBox(
                                          width: 80.0,
                                          child: ShadInput(
                                            controller:
                                                _linearityNominalControllers[index],
                                            // decoration: ShadDecoration(
                                            //   // labelPadding: EdgeInsets.all(100.0),
                                            // ),
                                            placeholder: Text('0'),
                                          ),
                                        ),
                                        SizedBox(width: 10.0),
                                        SizedBox(
                                          width: 210.0,
                                          child: Column(
                                            children: [
                                              // Show total massa_konvensional for any AT selection
                                              if (_getTotalMassaKonvensional(
                                                    index,
                                                  ) >
                                                  0)
                                                Container(
                                                  width: double.infinity,
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 6,
                                                        vertical: 4,
                                                      ),
                                                  margin: const EdgeInsets.only(
                                                    bottom: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: Colors.green.shade50,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          4,
                                                        ),
                                                    border: Border.all(
                                                      color:
                                                          Colors.green.shade200,
                                                    ),
                                                  ),
                                                  child: Text(
                                                    _getTotalMassaKonvensionalDisplay(
                                                      index,
                                                    ),
                                                    style: const TextStyle(
                                                      fontSize: 10,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.green,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ListView.builder(
                                                shrinkWrap:
                                                    true, // ← This is key
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                itemCount: 5,
                                                itemBuilder: (context, index2) {
                                                  return _buildSearchableLinearityAnakTimbangDropdown(
                                                    index,
                                                    index2,
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(width: 10.0),
                                        SizedBox(
                                          width: 200.0,
                                          child: ListView.builder(
                                            shrinkWrap: true, // ← This is key
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            itemCount: 3,
                                            itemBuilder: (context, index2) {
                                              return _buildLinearityReading(
                                                index,
                                                index2,
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 4),
                                    // Pass/Fail button below the row
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: SizedBox(
                                        width: 100,
                                        child: ShadButton(
                                          size: ShadButtonSize.sm,
                                          onPressed: () {},
                                          gradient: LinearGradient(
                                            colors:
                                                _getLinearityPassFailColorGradient(
                                                  index,
                                                ),
                                          ),
                                          child: Text(
                                            _getLinearityPassFailText(index),
                                            style: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Divider(
                                      height: 1.0,
                                      color: const Color.fromARGB(
                                        255,
                                        197,
                                        195,
                                        195,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 60.0),
                    ShadButton(
                      onPressed: () {},
                      leading: const Icon(LucideIcons.plus),
                      child: const Text('Add Linearity'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16.0),

            // --ENDPAGE LINEARITY

            // --PAGE ECCENTRICITY
            ShadCard(
              // width: 350,
              title: Text('Eccentricity', style: theme.textTheme.h4),

              // description: const Text('Deploy your new project in one-click.'),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WarningAlert(
                      title: 'Catatan:',
                      text:
                          '1. Gunakan Anak Timbang Tunggal\n'
                          '2. 2.Gunakan anak timbang anatara 1/3 - ½ dari kapasitas maksimal',
                    ),
                    const Text('Nominal'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 380.0,
                      child: ShadInput(
                        controller: _eccentricityNominalController,
                        placeholder: Text('Nominal'),
                        trailing: Text(_selectedDayaBacaR1Unit),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Daya Baca Selection for Eccentricity
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: GestureDetector(
                        onTap: () => _showEccentricityDayaBacaDialog(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 16,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Daya Baca $_selectedEccentricityDayaBaca',
                                  style: const TextStyle(color: Colors.black),
                                ),
                              ),
                              const Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: 5, // Number of items
                            itemBuilder: (context, index) {
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 60.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          eccentricity[index]['label'] ?? '',
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatEccentricityLeftYellowBox(),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _eccentricityValueControllers[index],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatEccentricityRightYellowBox(
                                            index,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              ShadButton(
                                size: ShadButtonSize.lg,
                                onPressed: () {},
                                gradient: LinearGradient(
                                  colors:
                                      _getEccentricityPassFailColorGradient(),
                                ),
                                child: Text(_getEccentricityPassFailText()),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16.0),

            // --ENDPAGE ECCENTRICITY

            // --PAGE HISTERISYS
            ShadCard(
              // width: 350,
              title: Text('Histerisys', style: theme.textTheme.h4),

              // description: const Text('Deploy your new project in one-click.'),
              child: Container(
                width: 400.0,
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        SizedBox(width: 32.0, child: const Text('M')),
                        SizedBox(
                          width: 320.0,
                          child: ShadInput(
                            controller: _histerisysMController,
                            placeholder: Text('M'),
                            trailing: Text(_selectedDayaBacaR1Unit),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),
                    Row(
                      children: [
                        SizedBox(width: 32.0, child: const Text('M\'')),
                        SizedBox(
                          width: 320.0,
                          child: ShadInput(
                            controller: _histerisysMPrimeController,
                            placeholder: Text('M\''),
                            trailing: Text(_selectedDayaBacaR1Unit),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Daya Baca Selection for Histerisys
                    const Text('Pilih Daya Baca'),
                    const SizedBox(height: 6),
                    SizedBox(
                      width: 200.0,
                      child: GestureDetector(
                        onTap: () => _showHisterisysDayaBacaDialog(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 16,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  'Daya Baca $_selectedHisterisysDayaBaca',
                                  style: const TextStyle(color: Colors.black),
                                ),
                              ),
                              const Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: ListView.builder(
                            shrinkWrap: true, // ← This is key
                            physics: NeverScrollableScrollPhysics(),
                            itemCount:
                                histerisys.length +
                                (histerisys.length ~/ 8), // Number of items
                            itemBuilder: (context, index) {
                              final originalIndex = index - (index ~/ (8 + 1));
                              if ((index + 1) % 9 == 0) {
                                return SizedBox(height: 40.0);
                              }
                              // const
                              return Container(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 60.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10.0),
                                          bottomLeft: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          histerisys[originalIndex]['label']!,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                        right: 2.0,
                                        // left: 2.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        border: Border(
                                          left: BorderSide(
                                            width: 1.0,
                                            color: const Color.fromARGB(
                                              255,
                                              197,
                                              195,
                                              195,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatHisterisysLeftYellowBox(
                                            originalIndex,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: ShadInput(
                                        controller:
                                            _histerisysValueControllers[originalIndex],
                                        placeholder: Text('0'),
                                      ),
                                    ),
                                    Container(
                                      width: 120.0,
                                      padding: EdgeInsets.only(
                                        top: 10.0,
                                        bottom: 10.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.yellow,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10.0),
                                          bottomRight: Radius.circular(10.0),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          _formatHisterisysRightYellowBox(
                                            originalIndex,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16.0),
            // --ENDPAGE HISTERISYS

            // --PAGE AFTER
            ShadCard(
              // width: 350,
              title: Text('After', style: theme.textTheme.h4),
              footer: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ShadButton.outline(
                    child: const Text('Cancel'),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  ShadButton(
                    onPressed: _isSaving ? null : _submitForm,
                    child: _isSaving
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('Submit'),
                  ),
                ],
              ),
              // description: const Text('Deploy your new project in one-click.'),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text('Temperature'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _afterTempController,
                      placeholder: const Text('Temperature'),
                      trailing: const Text("˚c"),
                    ),
                    const SizedBox(height: 16),
                    const Text('Humidity'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _afterHumidityController,
                      placeholder: const Text('Humidity'),
                      trailing: const Text("˚%"),
                    ),
                    const SizedBox(height: 16),
                    const Text('Barrometer'),
                    const SizedBox(height: 6),
                    ShadInput(
                      controller: _afterBarrometerController,
                      placeholder: const Text('Barrometer'),
                      trailing: const Text("kpa"),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),

            // --ENDPAGE AFTER
            // ConstrainedBox(
            //   constraints: BoxConstraints(
            //     maxHeight: MediaQuery.of(context).size.height * 0.8,
            //   ),
            //   child: Row(
            //     children: [
            //       Flexible(child: TablePage()),
            //       SizedBox(width: 8),
            //       Flexible(child: TablePage()),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() => _isSaving = true);

      // final formDataS = collectFormData();
      // final jsonString = JsonEncoder.withIndent('  ').convert(formDataS);
      // print('Form Data JSON:\n$jsonString');

      try {
        // Handle new timbangan if needed
        String timbanganUuid;
        if (_isAddNewTimbangan) {
          // Validate new timbangan fields
          if (_newTimbanganIdController.text.trim().isEmpty ||
              _newTimbanganTypeController.text.trim().isEmpty ||
              _newTimbanganBrandController.text.trim().isEmpty ||
              _newTimbanganModelController.text.trim().isEmpty ||
              _newTimbanganSNRController.text.trim().isEmpty ||
              _newTimbanganCapacityController.text.trim().isEmpty ||
              _newTimbanganLocationController.text.trim().isEmpty) {
            setState(() => _isSaving = false);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Please fill all new timbangan fields'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }

          // Save new timbangan and get UUID
          timbanganUuid = await _saveNewTimbangan();
        } else {
          // Use selected timbangan
          if (_selectedTimbangan == null || _selectedTimbangan!.isEmpty) {
            setState(() => _isSaving = false);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Please select a timbangan'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }
          timbanganUuid = _selectedTimbangan!;
        }

        // Collect all form data
        Map<String, dynamic> formData = {
          'id': 3,
          'uuid': const Uuid().v4(),
          'orderNumber': _orderNumberController.text,
          'companyName': _companyNameController.text,
          'companyAddress': _companyAddressController.text,
          'contactPerson': _contactPersonController.text,
          'cpDivision': _cpDivisionController.text,
          'certNumber': _certNumberController.text,
          'dateCalibration': _selectedCalibrationDate,
          'technicianUuid': _selectedTechnician.isNotEmpty
              ? _selectedTechnician
              : null,
          'timbanganUuid': timbanganUuid,
          'dayaBacaR1Value': _dayaBacaR1ValueController.text.isNotEmpty
              ? double.tryParse(_dayaBacaR1ValueController.text)
              : null,
          'dayaBacaR1Unit': _selectedDayaBacaR1Unit,
          'dayaBacaR2Value': _dayaBacaR2ValueController.text.isNotEmpty
              ? double.tryParse(_dayaBacaR2ValueController.text)
              : null,
          'dayaBacaR2Unit': _selectedDayaBacaR2Unit,
          'dayaBacaR3Value': _dayaBacaR3ValueController.text.isNotEmpty
              ? double.tryParse(_dayaBacaR3ValueController.text)
              : null,
          'dayaBacaR3Unit': _selectedDayaBacaR3Unit,
          'accurationPercentage': _accurationController.text.isNotEmpty
              ? double.tryParse(_accurationController.text)
              : null,
          'beforeTemp': _beforeTempController.text.isNotEmpty
              ? double.tryParse(_beforeTempController.text)
              : null,
          'beforeHumidity': _beforeHumidityController.text.isNotEmpty
              ? double.tryParse(_beforeHumidityController.text)
              : null,
          'beforeBarrometer': _beforeBarrometerController.text.isNotEmpty
              ? double.tryParse(_beforeBarrometerController.text)
              : null,
          'afterTemp': _afterTempController.text.isNotEmpty
              ? double.tryParse(_afterTempController.text)
              : null,
          'afterHumidity': _afterHumidityController.text.isNotEmpty
              ? double.tryParse(_afterHumidityController.text)
              : null,
          'afterBarrometer': _afterBarrometerController.text.isNotEmpty
              ? double.tryParse(_afterBarrometerController.text)
              : null,
          'standardUuid': _selectedStandard.isNotEmpty
              ? _selectedStandard
              : null,
          // Add pre-adjustment data
          'anakTimbangList': _weightSetControllers
              .map((c) => c.text)
              .where((s) => s.isNotEmpty)
              .join(','),
          'preadjustmentAnakTimbangUuid':
              _preAdjustmentIdController.text.isNotEmpty
              ? _preAdjustmentIdController.text
              : null,
          'preadjustmentNominal':
              _preAdjustmentNominalController.text.isNotEmpty
              ? double.tryParse(_preAdjustmentNominalController.text)
              : null,
          'preadjustmentValue1':
              _preAdjustmentValueControllers.isNotEmpty &&
                  _preAdjustmentValueControllers[0].text.isNotEmpty
              ? double.tryParse(_preAdjustmentValueControllers[0].text)
              : null,
          'preadjustmentValue2':
              _preAdjustmentValueControllers.length > 1 &&
                  _preAdjustmentValueControllers[1].text.isNotEmpty
              ? double.tryParse(_preAdjustmentValueControllers[1].text)
              : null,
          'preadjustmentValue3':
              _preAdjustmentValueControllers.length > 2 &&
                  _preAdjustmentValueControllers[2].text.isNotEmpty
              ? double.tryParse(_preAdjustmentValueControllers[2].text)
              : null,
          'preadjustmentValue4':
              _preAdjustmentValueControllers.length > 3 &&
                  _preAdjustmentValueControllers[3].text.isNotEmpty
              ? double.tryParse(_preAdjustmentValueControllers[3].text)
              : null,
          'branchCreator': _selectedBranch.isNotEmpty ? _selectedBranch : null,
          'status': _status,
          'createdAt': DateTime.now(),

          // Pre-adjustment metadata as JSON string
          'preAdjustmentMetadata': {
            'selectedDayaBaca': _selectedPreAdjustmentDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'nominal': _preAdjustmentNominalController.text,
            'rows': List.generate(4, (index) {
              return {
                'index': index,
                'leftValue': _formatYellowBoxValue(index),
                'middleValue': _preAdjustmentValueControllers[index].text,
                'rightValue': _formatRightYellowBoxValue(index),
                'decimalDigits': _getDecimalDigits(_getSelectedDayaBacaValue()),
                'minimumDecimal':
                    _getDecimalDigits(_getSelectedDayaBacaValue()) > 0
                    ? (1.0 /
                              math.pow(
                                10,
                                _getDecimalDigits(_getSelectedDayaBacaValue()),
                              ))
                          .toString()
                    : '0.1',
              };
            }),
            'calculationInfo': {
              'selectedDayaBacaValue': _getSelectedDayaBacaValue(),
              'decimalDigits': _getDecimalDigits(_getSelectedDayaBacaValue()),
              'leftBoxFormula': 'nominal_value.toStringAsFixed(decimal_digits)',
              'rightBoxFormula':
                  'left_value + (middle_value * minimum_decimal)',
              'minimumDecimalFormula': '1.0 / (10^decimal_digits)',
            },
          },

          // Repeat Half Capacity metadata as JSON string
          'repeatHalfCapacityMetadata': {
            'selectedDayaBaca': _selectedRepeatHalfDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'nominal': _repeatHalfNominalController.text,
            'rows': List.generate(20, (index) {
              return {
                'index': index,
                'leftValue': _formatRepeatHalfLeftYellowBoxValue(index),
                'middleValue': index < 10
                    ? _repeatHalfValueControllers1[index].text
                    : _repeatHalfValueControllers2[index - 10].text,
                'rightValue': _formatRepeatHalfRightYellowBoxValue(index),
                'decimalDigits': _getDecimalDigits(
                  _getSelectedRepeatHalfDayaBacaValue(),
                ),
                'minimumDecimal':
                    _getDecimalDigits(_getSelectedRepeatHalfDayaBacaValue()) > 0
                    ? (1.0 /
                              math.pow(
                                10,
                                _getDecimalDigits(
                                  _getSelectedRepeatHalfDayaBacaValue(),
                                ),
                              ))
                          .toString()
                    : '0.1',
                'nominalUsed': index < 10
                    ? '0'
                    : _repeatHalfNominalController.text,
              };
            }),
            'calculationInfo': {
              'selectedDayaBacaValue': _getSelectedRepeatHalfDayaBacaValue(),
              'decimalDigits': _getDecimalDigits(
                _getSelectedRepeatHalfDayaBacaValue(),
              ),
              'leftBoxFormula':
                  'first_10_indices_use_0_nominal_next_10_use_filled_nominal',
              'rightBoxFormula':
                  'left_value + (middle_value * minimum_decimal)',
              'minimumDecimalFormula': '1.0 / (10^decimal_digits)',
            },
          },

          // Repeat Full Capacity metadata as JSON string
          'repeatFullCapacityMetadata': {
            'selectedDayaBaca': _selectedRepeatFullDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'nominal': _repeatFullNominalController.text,
            'rows': List.generate(20, (index) {
              return {
                'index': index,
                'leftValue': _formatRepeatFullLeftYellowBoxValue(index),
                'middleValue': index < 10
                    ? _repeatFullValueControllers1[index].text
                    : _repeatFullValueControllers2[index - 10].text,
                'rightValue': _formatRepeatFullRightYellowBoxValue(index),
                'decimalDigits': _getDecimalDigits(
                  _getSelectedRepeatFullDayaBacaValue(),
                ),
                'minimumDecimal':
                    _getDecimalDigits(_getSelectedRepeatFullDayaBacaValue()) > 0
                    ? (1.0 /
                              math.pow(
                                10,
                                _getDecimalDigits(
                                  _getSelectedRepeatFullDayaBacaValue(),
                                ),
                              ))
                          .toString()
                    : '0.1',
                'nominalUsed': index < 10
                    ? '0'
                    : _repeatFullNominalController.text,
              };
            }),
            'calculationInfo': {
              'selectedDayaBacaValue': _getSelectedRepeatFullDayaBacaValue(),
              'decimalDigits': _getDecimalDigits(
                _getSelectedRepeatFullDayaBacaValue(),
              ),
              'leftBoxFormula':
                  'first_10_indices_use_0_nominal_next_10_use_filled_nominal',
              'rightBoxFormula':
                  'left_value + (middle_value * minimum_decimal)',
              'minimumDecimalFormula': '1.0 / (10^decimal_digits)',
            },
          },

          // Eccentricity metadata as JSON string
          'eccentricityMetadata': {
            'selectedDayaBaca': _selectedEccentricityDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'nominal': _eccentricityNominalController.text,
            'rows': List.generate(5, (index) {
              return {
                'index': index,
                'label': eccentricity[index]['label'],
                'leftValue': _formatEccentricityLeftYellowBox(),
                'middleValue': _eccentricityValueControllers[index].text,
                'rightValue': _formatEccentricityRightYellowBox(index),
                'decimalDigits': _getEccentricityDecimalDigits(),
                'minimumDecimal': _getEccentricityDecimalDigits() > 0
                    ? (1.0 / math.pow(10, _getEccentricityDecimalDigits()))
                          .toString()
                    : '0.1',
              };
            }),
            'calculationInfo': {
              'selectedDayaBacaValue': _getSelectedEccentricityDayaBacaValue(),
              'decimalDigits': _getEccentricityDecimalDigits(),
              'leftBoxFormula': 'nominal_value.toStringAsFixed(decimal_digits)',
              'rightBoxFormula':
                  'left_value + (middle_value * minimum_decimal)',
              'minimumDecimalFormula': '1.0 / (10^decimal_digits)',
            },
            'passFailInfo': _calculateEccentricityPassFail(),
          },

          // Histerisys metadata as JSON string
          'histerisysMetadata': {
            'selectedDayaBaca': _selectedHisterisysDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'nominalM': _histerisysMController.text,
            'nominalMPrime': _histerisysMPrimeController.text,
            'rows': List.generate(histerisys.length, (index) {
              return {
                'index': index,
                'label': histerisys[index]['label'],
                'leftValue': _formatHisterisysLeftYellowBox(index),
                'middleValue': _histerisysValueControllers[index].text,
                'rightValue': _formatHisterisysRightYellowBox(index),
                'decimalDigits': _getHisterisysDecimalDigits(),
                'minimumDecimal': _getHisterisysDecimalDigits() > 0
                    ? (1.0 / math.pow(10, _getHisterisysDecimalDigits()))
                          .toString()
                    : '0.1',
              };
            }),
            'calculationInfo': {
              'selectedDayaBacaValue': _getSelectedHisterisysDayaBacaValue(),
              'decimalDigits': _getHisterisysDecimalDigits(),
              'leftBoxFormula': 'special_logic_for_M+M_prime_Zero_and_others',
              'rightBoxFormula':
                  'base_value + (middle_value * minimum_decimal)',
              'minimumDecimalFormula': '1.0 / (10^decimal_digits)',
            },
            'passFailInfo': _calculateHisterisysPassFail(),
          },

          // Linearity metadata as JSON string
          'linearityMetadata': {
            'selectedDayaBaca': _selectedLinearityDayaBaca,
            'dayaBacaValues': {
              '1': _dayaBacaR1ValueController.text,
              '2': _dayaBacaR2ValueController.text,
              '3': _dayaBacaR3ValueController.text,
            },
            'dayaBacaUnits': {
              '1': _selectedDayaBacaR1Unit,
              '2': _selectedDayaBacaR2Unit,
              '3': _selectedDayaBacaR3Unit,
            },
            'unit': _linearityUnitController.text,
            'rows': List.generate(10, (index) {
              return {
                'index': index,
                'nominal': _linearityNominalControllers[index].text,
                'conventionalSelections': List.generate(5, (subIndex) {
                  return {
                    'subIndex': subIndex,
                    'selectedAnakTimbangUuid':
                        _linearityConventionalControllers[index][subIndex].text,
                    'massaKonvensional': _getLinearityMassaKonvensional(
                      index,
                      subIndex,
                    ),
                    'massaKonvensionalDisplay':
                        _getLinearityMassaKonvensionalDisplay(index, subIndex),
                  };
                }),
                'totalMassaKonvensional': _getTotalMassaKonvensional(index),
                'totalMassaKonvensionalDisplay':
                    _getTotalMassaKonvensionalDisplay(index),
                'readings': List.generate(3, (readingIndex) {
                  return {
                    'readingIndex': readingIndex,
                    'inputValue':
                        _linearityReadingControllers[index][readingIndex].text,
                    'rightYellowBoxValue': _formatLinearityRightYellowBox(
                      index,
                      readingIndex,
                    ),
                    'parsedValue': _parseRightYellowBoxValue(
                      _formatLinearityRightYellowBox(index, readingIndex),
                    ),
                  };
                }),
                'calculationInfo': {
                  'selectedDayaBacaValue': _getSelectedLinearityDayaBacaValue(),
                  'decimalDigits': _getLinearityDecimalDigits(),
                  'readingResult': _calculateLinearityReadingResult(index),
                  'linearityDeviasi': _calculateLinearityDeviasi(index),
                  'mpe': _calculateLinearityMPE(index),
                  'readingFormula':
                      '((second + third) / 2) + ((first + next_row_first) / 2)',
                  'deviasiFormula': 'total_massa_konvensional - readingResult',
                  'mpeFormula': 'total_massa_konvensional × (akurasi / 2)',
                  'rightBoxFormula':
                      'base_value + (reading_value × minimum_decimal)',
                  'baseValueLogic': 'index_0=0, index_1&2=nominal',
                },
                'passFailInfo': _calculateLinearityPassFail(index),
              };
            }),
          },
        };
        String jsonRes = toPrettyJson(formData);
        print('To save \n$jsonRes');
        setState(() => _isSaving = false);

        final allMetadata = jsonEncode({
          'preAdjustment': {
            ...formData['preAdjustmentMetadata'],
            'description':
                'Pre-adjustment calibration to set initial scale parameters',
            'purpose':
                'Establish baseline readings before main calibration tests',
          },
          'repeatHalfCapacity': {
            ...formData['repeatHalfCapacityMetadata'],
            'passFailInfo': _calculateRepeatHalfPassFail(),
            'description':
                'Repeatability test at half capacity to verify measurement consistency',
            'purpose':
                'Ensure scale provides consistent readings under repeated loading',
            'passFailCriteria':
                'Standard deviation of differences ≤ selected daya baca value',
            'calculationFormula': {
              'differences': 'right_yellow_box[10-19] - right_yellow_box[0-9]',
              'standardDeviation': 'sqrt(sum((xi - mean)²) / (n-1))',
              'passCondition': 'standardDeviation ≤ selectedDayaBacaValue',
            },
          },
          'repeatFullCapacity': {
            ...formData['repeatFullCapacityMetadata'],
            'passFailInfo': _calculateRepeatFullPassFail(),
            'description':
                'Repeatability test at full capacity to verify measurement consistency',
            'purpose':
                'Ensure scale provides consistent readings under repeated loading at maximum capacity',
            'passFailCriteria':
                'Standard deviation of differences ≤ selected daya baca value',
            'calculationFormula': {
              'differences': 'right_yellow_box[10-19] - right_yellow_box[0-9]',
              'standardDeviation': 'sqrt(sum((xi - mean)²) / (n-1))',
              'passCondition': 'standardDeviation ≤ selectedDayaBacaValue',
            },
          },
          'eccentricity': {
            ...formData['eccentricityMetadata'],
            'description':
                'Eccentricity test to verify scale accuracy at different load positions',
            'purpose':
                'Ensure scale readings are consistent regardless of load placement position',
            'passFailCriteria':
                'All position deviations must be within MPE (Maximum Permissible Error)',
            'calculationFormula': {
              'deviation': 'reading_value - nominal_value',
              'mpe': 'nominal_value × (accuracy_percentage / 2)',
              'passCondition': 'abs(deviation) < mpe for all positions',
            },
            'positions': [
              'C (Center)',
              'F (Front)',
              'B (Back)',
              'R (Right)',
              'L (Left)',
            ],
          },
          'histerisys': {
            ...formData['histerisysMetadata'],
            'passFailInfo': _calculateHisterisysPassFail(),
            'description':
                'Hysteresis test to verify scale consistency during loading and unloading cycles',
            'purpose':
                'Ensure scale returns to same reading after loading and unloading cycles',
            'passFailCriteria':
                'Hysteresis error must be within acceptable limits',
            'calculationFormula': {
              'hysteresisError':
                  'max_difference_between_ascending_and_descending_readings',
              'passCondition':
                  'hysteresisError ≤ maximum_permissible_hysteresis',
            },
            'loadingSequence': [
              'M(p1) - Initial load',
              'M+M\' - Combined load ascending',
              'M(q1) - Reading at combined load',
              'Zero - Return to zero',
              'M+M\' - Combined load descending',
              'M(q2) - Reading at combined load',
              'Zero - Return to zero',
              'M(p2) - Final load',
            ],
          },
          'linearity': {
            ...formData['linearityMetadata'],
            'description':
                'Linearity test to verify scale accuracy across its measurement range',
            'purpose':
                'Ensure scale maintains accuracy from minimum to maximum capacity',
            'passFailCriteria':
                'Deviation from expected reading must be within MPE for each test point',
            'calculationFormula': {
              'readingResult':
                  '((reading2 + reading3) / 2) + ((reading1 + next_reading1) / 2)',
              'deviation': 'total_massa_konvensional - readingResult',
              'mpe': 'total_massa_konvensional × (accuracy_percentage / 2)',
              'passCondition': 'abs(deviation) ≤ mpe',
            },
          },
          'overallSummary': {
            'totalTests': 5,
            'testTypes': [
              'Pre-adjustment',
              'Repeat Half Capacity',
              'Repeat Full Capacity',
              'Eccentricity',
              'Linearity',
              'Hysteresis',
            ],
            'calibrationStandard': 'OIML R76-1:2006 & OIML R111-1:2004',
            'passFailSummary': {
              'repeatHalfCapacity': _calculateRepeatHalfPassFail()['result'],
              'repeatFullCapacity': _calculateRepeatFullPassFail()['result'],
              'eccentricity': _calculateEccentricityPassFail()['result'],
              'linearity': _getLinearityOverallPassFail(),
              'hysteresis': _calculateHisterisysPassFail()['result'],
            },
          },
        });

        // Create a new request object
        var newRequest = CalibrationRequest(
          uuid: formData['uuid'],
          orderNumber: formData['orderNumber'],
          companyName: formData['companyName'],
          companyAddress: formData['companyAddress'],
          contactPerson: formData['contactPerson'],
          cpDivision: formData['cpDivision'],
          certNumber: formData['certNumber'],
          dateCalibration: formData['dateCalibration'],
          technicianUuid: formData['technicianUuid'],
          timbanganUuid: formData['timbanganUuid'],
          dayaBacaR1Value: formData['dayaBacaR1Value'],
          dayaBacaR1Unit: formData['dayaBacaR1Unit'],
          dayaBacaR2Value: formData['dayaBacaR2Value'],
          dayaBacaR2Unit: formData['dayaBacaR2Unit'],
          accurationPercentage: formData['accurationPercentage'],
          beforeTemp: formData['beforeTemp'],
          beforeHumidity: formData['beforeHumidity'],
          beforeBarrometer: formData['beforeBarrometer'],
          afterTemp: formData['afterTemp'],
          afterHumidity: formData['afterHumidity'],
          afterBarrometer: formData['afterBarrometer'],
          standardUuid: formData['standardUuid'],
          // Add pre-adjustment data
          anakTimbangList: formData['anakTimbangList'],
          preadjustmentAnakTimbangUuid:
              formData['preadjustmentAnakTimbangUuid'],
          preadjustmentNominal: formData['preadjustmentNominal'],
          preadjustmentValue1: formData['preadjustmentValue1'],
          preadjustmentValue2: formData['preadjustmentValue2'],
          preadjustmentValue3: formData['preadjustmentValue3'],
          preadjustmentValue4: formData['preadjustmentValue4'],
          allMetadata: allMetadata,
          branchCreator: formData['branchCreator'],
          status: formData['status'],
          createdAt: formData['createdAt'],
        );

        // Save to local database
        final requestId = await widget.repository.insertCalibrationRequestLocal(
          newRequest,
        );

        // Update the newRequest object with the actual database ID
        newRequest = newRequest.copyWith(id: requestId);

        // Now save related data
        await _saveRelatedData(newRequest.uuid);

        // Generate Excel file
        String? excelPath;
        try {
          excelPath = await _generateExcelReportSilent(formData['uuid']);
        } catch (excelError) {
          // Excel generation failed, but data is saved
          print('Excel generation failed: $excelError');
        }

        // Trigger timbangan sync if timbangan is new
        if (_isAddNewTimbangan) {
          _triggerTimbanganSync();
        }

        // Trigger calibration sync in background
        _triggerCalibrationSync();

        // Show success message and navigate back
        if (mounted) {
          final message = excelPath != null
              ? 'Calibration request saved successfully!\nExcel and PDF reports generated automatically.'
              : 'Calibration request saved successfully (Report generation failed)';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: const Duration(seconds: 4),
            ),
          );

          // Return to previous screen with updated request (now has correct ID)
          Navigator.pop(context, newRequest);
        }
      } catch (e) {
        setState(() => _isSaving = false);
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Failed to save request: $e')));
        }
      }
    } else {
      // Form validation failed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please fill in all required fields')),
        );
      }
    }
  }

  Future<void> _saveRelatedData(String requestId) async {
    // 1. Save Repeat Half Capacity (both value sets as JSON)
    final halfCapacityJson1 = jsonEncode({
      'values': _repeatHalfValueControllers1.map((c) => c.text).toList(),
      'type': 'values_1',
      'nominal': _repeatHalfNominalController.text,
    });

    final repeatHalf1 = CalibrationRepeatCapacity(
      calibrationRequestUuid: requestId,
      repeatType: 'half',
      rangeType: 1,
      capacityNominal: double.tryParse(_repeatHalfNominalController.text) ?? 0,
      capacityValue: halfCapacityJson1,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertRepeatCapacityLocal(repeatHalf1);

    // Save second set of half capacity values as JSON
    final halfCapacityJson2 = jsonEncode({
      'values': _repeatHalfValueControllers2.map((c) => c.text).toList(),
      'type': 'values_2',
      'nominal': _repeatHalfNominalController.text,
    });

    final repeatHalf2 = CalibrationRepeatCapacity(
      calibrationRequestUuid: requestId,
      repeatType: 'half',
      rangeType: 2,
      capacityNominal: double.tryParse(_repeatHalfNominalController.text) ?? 0,
      capacityValue: halfCapacityJson2,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertRepeatCapacityLocal(repeatHalf2);

    // 2. Save Repeat Full Capacity (both value sets as JSON)
    final fullCapacityJson1 = jsonEncode({
      'values': _repeatFullValueControllers1.map((c) => c.text).toList(),
      'type': 'values_1',
      'nominal': _repeatFullNominalController.text,
    });

    final repeatFull1 = CalibrationRepeatCapacity(
      calibrationRequestUuid: requestId,
      repeatType: 'full',
      rangeType: 1,
      capacityNominal: double.tryParse(_repeatFullNominalController.text) ?? 0,
      capacityValue: fullCapacityJson1,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertRepeatCapacityLocal(repeatFull1);

    // Save second set of full capacity values as JSON
    final fullCapacityJson2 = jsonEncode({
      'values': _repeatFullValueControllers2.map((c) => c.text).toList(),
      'type': 'values_2',
      'nominal': _repeatFullNominalController.text,
    });

    final repeatFull2 = CalibrationRepeatCapacity(
      calibrationRequestUuid: requestId,
      repeatType: 'full',
      rangeType: 2,
      capacityNominal: double.tryParse(_repeatFullNominalController.text) ?? 0,
      capacityValue: fullCapacityJson2,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertRepeatCapacityLocal(repeatFull2);

    // 3. Save Additional Capacity Card (only one allowed)
    if (_additionalCapacityCards.isNotEmpty) {
      final card = _additionalCapacityCards[0]; // Only one card allowed
      final nominalController =
          card['nominalController'] as TextEditingController;
      final valueControllers1 =
          card['valueControllers1'] as List<TextEditingController>;
      final valueControllers2 =
          card['valueControllers2'] as List<TextEditingController>;

      // Save half capacity with range_type = 2
      final additionalHalfCapacityJson = jsonEncode({
        'values': valueControllers1.map((c) => c.text).toList(),
        'type': 'values_1',
        'nominal': nominalController.text,
      });

      final additionalHalfCapacity = CalibrationRepeatCapacity(
        calibrationRequestUuid: requestId,
        repeatType: 'half',
        rangeType: 2,
        capacityNominal: double.tryParse(nominalController.text) ?? 0,
        capacityValue: additionalHalfCapacityJson,
        isPass: true,
        createdAt: DateTime.now(),
      );
      await widget.repository.insertRepeatCapacityLocal(additionalHalfCapacity);

      // Save full capacity with range_type = 2
      final additionalFullCapacityJson = jsonEncode({
        'values': valueControllers2.map((c) => c.text).toList(),
        'type': 'values_2',
        'nominal': nominalController.text,
      });

      final additionalFullCapacity = CalibrationRepeatCapacity(
        calibrationRequestUuid: requestId,
        repeatType: 'full',
        rangeType: 2,
        capacityNominal: double.tryParse(nominalController.text) ?? 0,
        capacityValue: additionalFullCapacityJson,
        isPass: true,
        createdAt: DateTime.now(),
      );
      await widget.repository.insertRepeatCapacityLocal(additionalFullCapacity);
    }

    // 4. Save Linearity data as JSON
    for (int i = 0; i < _linearityNominalControllers.length; i++) {
      if (_linearityNominalControllers[i].text.isNotEmpty) {
        // Create JSON structure for linearity values
        final linearityValueJson = jsonEncode({
          'nominal': _linearityNominalControllers[i].text,
          'conventional_values': List.generate(5, (subIndex) {
            return _linearityConventionalControllers[i][subIndex].text;
          }),
          'reading': List.generate(3, (readingIndex) {
            return _linearityReadingControllers[i][readingIndex].text;
          }),
          'unit': _linearityUnitController.text,
          'row_index': i,
        });

        final linearity = CalibrationLinearity(
          calibrationRequestUuid: requestId,
          linearityNominal:
              double.tryParse(_linearityNominalControllers[i].text) ?? 0,
          linearityValue: linearityValueJson,
          reading1:
              double.tryParse(_linearityReadingControllers[i][0].text) ?? 0,
          reading2:
              double.tryParse(_linearityReadingControllers[i][1].text) ?? 0,
          reading3:
              double.tryParse(_linearityReadingControllers[i][2].text) ?? 0,
          isPass: true,
          createdAt: DateTime.now(),
        );
        await widget.repository.insertLinearityLocal(linearity);
      }
    }

    // 4. Save Eccentricity data as JSON
    final eccentricityValueJson = jsonEncode({
      'nominal': _eccentricityNominalController.text,
      'values': _eccentricityValueControllers.map((c) => c.text).toList(),
      'positions': ['C', 'F', 'B', 'R', 'L'], // Standard eccentricity positions
    });

    final eccentricity = CalibrationEccentricity(
      calibrationRequestUuid: requestId,
      eccentricityNominal:
          double.tryParse(_eccentricityNominalController.text) ?? 0,
      eccentricityValue: eccentricityValueJson,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertEccentricityLocal(eccentricity);

    // 5. Save Hysteresis data as JSON
    final mValueJson = jsonEncode({
      'nominal': _histerisysMController.text,
      'values': _histerisysValueControllers.take(8).map((c) => c.text).toList(),
      'type': 'ascending',
    });

    final m1ValueJson = jsonEncode({
      'nominal': _histerisysMPrimeController.text,
      'values': _histerisysValueControllers.skip(8).map((c) => c.text).toList(),
      'type': 'descending',
    });

    final histerisys = CalibrationHisterisys(
      calibrationRequestUuid: requestId,
      mNominal: double.tryParse(_histerisysMController.text) ?? 0,
      m1Nominal: double.tryParse(_histerisysMPrimeController.text) ?? 0,
      mValue: mValueJson,
      m1Value: m1ValueJson,
      isPass: true,
      createdAt: DateTime.now(),
    );
    await widget.repository.insertHisterisysLocal(histerisys);
  }

  Map<String, dynamic> collectFormData() {
    final formData = <String, dynamic>{};

    // Branch Selection
    formData['branch'] = _selectedBranch;

    // Data Timbangan Section
    formData['date_calibration'] = _selectedCalibrationDate?.toIso8601String();
    formData['technician'] = _selectedTechnician;
    formData['order_number'] = _orderNumberController.text;
    formData['scale_id'] = _selectedScaleId;
    formData['daya_baca_1'] = {
      'value': _dayaBacaR1ValueController.text,
      'unit': _selectedDayaBacaR1Unit,
    };
    formData['daya_baca_2'] = {
      'value': _dayaBacaR2ValueController.text,
      'unit': _selectedDayaBacaR2Unit,
    };
    formData['daya_baca_3'] = {
      'value': _dayaBacaR3ValueController.text,
      'unit': _selectedDayaBacaR3Unit,
    };
    formData['accuration'] = _accurationController.text;

    // Before Section
    formData['before'] = {
      'temperature': _beforeTempController.text,
      'humidity': _beforeHumidityController.text,
      'barrometer': _beforeBarrometerController.text,
      'standard': _selectedStandard,
      'weight_sets': _weightSetControllers.map((c) => c.text).toList(),
    };

    // Pre-adjustment Section
    formData['pre_adjustment'] = {
      'nominal': _preAdjustmentNominalController.text,
      'values': _preAdjustmentValueControllers.map((c) => c.text).toList(),
      'id': _preAdjustmentIdController.text,
      'selected_daya_baca': _selectedPreAdjustmentDayaBaca,
    };

    // Repeat Half Capacity Section
    formData['repeat_half_capacity'] = {
      'nominal': _repeatHalfNominalController.text,
      'values_1': _repeatHalfValueControllers1.map((c) => c.text).toList(),
      'values_2': _repeatHalfValueControllers2.map((c) => c.text).toList(),
      'selected_daya_baca': _selectedRepeatHalfDayaBaca,
    };

    // Repeat Full Capacity Section
    formData['repeat_full_capacity'] = {
      'nominal': _repeatFullNominalController.text,
      'values_1': _repeatFullValueControllers1.map((c) => c.text).toList(),
      'values_2': _repeatFullValueControllers2.map((c) => c.text).toList(),
      'selected_daya_baca': _selectedRepeatFullDayaBaca,
    };

    // Second Capacity Card (only one allowed)
    if (_additionalCapacityCards.isNotEmpty) {
      final card = _additionalCapacityCards[0];
      final nominalController =
          card['nominalController'] as TextEditingController;
      final valueControllers1 =
          card['valueControllers1'] as List<TextEditingController>;
      final valueControllers2 =
          card['valueControllers2'] as List<TextEditingController>;

      formData['second_capacity'] = {
        'nominal': nominalController.text,
        'half_values': valueControllers1.map((c) => c.text).toList(),
        'full_values': valueControllers2.map((c) => c.text).toList(),
      };
    }

    // Linearity Section
    formData['linearity'] = {
      'unit': _linearityUnitController.text,
      'nominals': _linearityNominalControllers.map((c) => c.text).toList(),
      'conventionals': _linearityConventionalControllers
          .map((list) => list.map((c) => c.text).toList())
          .toList(),
      'readings': _linearityReadingControllers,
    };

    // Eccentricity Section
    formData['eccentricity'] = {
      'nominal': _eccentricityNominalController.text,
      'values': _eccentricityValueControllers.map((c) => c.text).toList(),
    };

    // Histerisys Section
    formData['histerisys'] = {
      'm': _histerisysMController.text,
      'm_prime': _histerisysMPrimeController.text,
      'values': _histerisysValueControllers.map((c) => c.text).toList(),
    };

    // After Section
    formData['after'] = {
      'temperature': _afterTempController.text,
      'humidity': _afterHumidityController.text,
      'barrometer': _afterBarrometerController.text,
    };

    return formData;
  }

  // Silent Excel generation (no dialog) - used during save flow
  Future<String> _generateExcelReportSilent(String requestId) async {
    try {
      final excelService = ExcelService(repository: widget.repository);
      final filePath = await excelService.generateCalibrationExcel(requestId);
      return filePath;
    } catch (e) {
      throw Exception('Failed to generate Excel report: $e');
    }
  }

  String toPrettyJson(Map<String, dynamic> data) {
    final encoded = data.map((key, value) {
      if (value is DateTime) {
        return MapEntry(key, value.toIso8601String());
      }
      return MapEntry(key, value);
    });
    return const JsonEncoder.withIndent('  ').convert(encoded);
  }
}
